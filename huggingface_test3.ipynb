{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 我们来探索一下真正的大语言对话模型\n", "- 这里以阿里最新发布的通义千问为例\n", "> **通义千问-1.8B**（Qwen-1.8B）是阿里云研发的通义千问大模型系列的18亿参数规模的模型。Qwen-1.8B是基于Transformer的大语言模型, 在超大规模的预训练数据上进行训练得到。预训练数据类型多样，覆盖广泛，包括大量网络文本、专业书籍、代码"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from transformers import AutoModelForCausalLM, AutoTokenizer\n", "from transformers.generation import GenerationConfig"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 需要安装一下工具库才能正常下载\n", "> `accelerate` || `tiktoken` || `einops` || `transformers_stream_generator==0.0.4`"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dff2384aed6e44ee8552f08076d994e4", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors.index.json:   0%|          | 0.00/14.7k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "703699bd11f8465993664f766a09acdf", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "52f514462f6448bbacc6c8a0538e4b17", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00001-of-00002.safetensors:   0%|          | 0.00/2.04G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "69d1eaba90324eca863f4727809926db", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00002-of-00002.safetensors:   0%|          | 0.00/1.63G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["The model is automatically converting to bf16 for faster inference. If you want to disable the automatic precision, please manually add bf16/fp16/fp32=True to \"AutoModelForCausalLM.from_pretrained\".\n", "Try importing flash-attention for faster inference...\n", "Warning: import flash_attn rotary fail, please install FlashAttention rotary to get higher efficiency https://github.com/Dao-AILab/flash-attention/tree/main/csrc/rotary\n", "Warning: import flash_attn rms_norm fail, please install FlashAttention layer_norm to get higher efficiency https://github.com/Dao-AILab/flash-attention/tree/main/csrc/layer_norm\n", "Warning: import flash_attn fail, please install FlashAttention to get higher efficiency https://github.com/Dao-AILab/flash-attention\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e5279f21b87643d7893eae325a83e294", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dc05f64df95b4ccaa7b4ada7523d822c", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/249 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tokenizer = AutoTokenizer.from_pretrained(\"Qwen/Qwen-1_8B-Chat\",trust_remote_code = True)\n", "model = AutoModelForCausalLM.from_pretrained(\"Qwen/Qwen-1_8B-Chat\", device_map=\"auto\", trust_remote_code=True).eval()"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["QWenTokenizer(name_or_path='Qwen/Qwen-1_8B-Chat', vocab_size=151851, model_max_length=8192, is_fast=False, padding_side='right', truncation_side='right', special_tokens={}, clean_up_tokenization_spaces=True),  added_tokens_decoder={\n", "\t\n", "}"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/plain": ["QWenConfig {\n", "  \"_name_or_path\": \"Qwen/Qwen-1_8B-Chat\",\n", "  \"architectures\": [\n", "    \"QWenLMHeadModel\"\n", "  ],\n", "  \"attn_dropout_prob\": 0.0,\n", "  \"auto_map\": {\n", "    \"AutoConfig\": \"Qwen/Qwen-1_8B-Chat--configuration_qwen.QWenConfig\",\n", "    \"AutoModelForCausalLM\": \"Qwen/Qwen-1_8B-Chat--modeling_qwen.QWenLMHeadModel\"\n", "  },\n", "  \"bf16\": true,\n", "  \"emb_dropout_prob\": 0.0,\n", "  \"fp16\": false,\n", "  \"fp32\": false,\n", "  \"hidden_size\": 2048,\n", "  \"initializer_range\": 0.02,\n", "  \"intermediate_size\": 11008,\n", "  \"kv_channels\": 128,\n", "  \"layer_norm_epsilon\": 1e-06,\n", "  \"max_position_embeddings\": 8192,\n", "  \"model_type\": \"qwen\",\n", "  \"no_bias\": true,\n", "  \"num_attention_heads\": 16,\n", "  \"num_hidden_layers\": 24,\n", "  \"onnx_safe\": null,\n", "  \"rotary_emb_base\": 10000,\n", "  \"rotary_pct\": 1.0,\n", "  \"scale_attn_weights\": true,\n", "  \"seq_length\": 8192,\n", "  \"softmax_in_fp32\": false,\n", "  \"tie_word_embeddings\": false,\n", "  \"tokenizer_class\": \"QWenTokenizer\",\n", "  \"transformers_version\": \"4.35.2\",\n", "  \"use_cache\": true,\n", "  \"use_cache_kernel\": false,\n", "  \"use_cache_quantization\": false,\n", "  \"use_dynamic_ntk\": true,\n", "  \"use_flash_attn\": true,\n", "  \"use_logn_attn\": true,\n", "  \"vocab_size\": 151936\n", "}"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["model.config"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你好！很高兴能和你聊天。有什么我能帮你的吗？ [('你好呀，小通', '你好！很高兴能和你聊天。有什么我能帮你的吗？')]\n"]}], "source": ["# 第一轮对话 1st dialogue turn\n", "response, history = model.chat(tokenizer, \"你好呀，小通\", history=None)\n", "print(response,history)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中国足球在过去的几年中经历了一些起伏，这主要是由以下几个方面造成的：\n", "\n", "1. 技术落后：虽然中国拥有庞大的足球人口，但中国足球的整体技术水平仍然相对较弱。这主要体现在球队的技术能力、球员的战术素养等方面。\n", "\n", "2. 教育水平较低：中国的青少年足球教育起步较晚，因此大多数球员的文化素质和体育技能都不足。这也影响了中国足球的发展。\n", "\n", "3. 球员流动性大：中国足球的球员转会制度相对复杂，容易导致球员流动率高。这不仅对俱乐部造成损失，也会影响国家队的战斗力。\n", "\n", "4. 资源投入不足：虽然近年来中国在足球领域的投资有所增加，但在硬件设施、教练队伍等方面仍有待提高。\n", "\n", "5. 企业赞助有限：中国企业对足球的关注度并不高，这意味着缺乏足够的资金来支持足球事业的发展。\n", "\n", "这些因素共同作用，使得中国足球在过去几年中成绩不佳。但是，我们相信随着国家和社会对足球的认可度不断提高，中国足球还有很大的发展空间。\n"]}], "source": ["# 第二轮对话 2nd dialogue turn\n", "response, history = model.chat(tokenizer, \"请你帮我深入分析一下中国足球比较失败的原因\", history=history)\n", "print(response)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[('你好呀，小通', '你好！很高兴能和你聊天。有什么我能帮你的吗？'),\n", " ('请你帮我深入分析一下中国足球比较失败的原因',\n", "  '中国足球在过去的几年中经历了一些起伏，这主要是由以下几个方面造成的：\\n\\n1. 技术落后：虽然中国拥有庞大的足球人口，但中国足球的整体技术水平仍然相对较弱。这主要体现在球队的技术能力、球员的战术素养等方面。\\n\\n2. 教育水平较低：中国的青少年足球教育起步较晚，因此大多数球员的文化素质和体育技能都不足。这也影响了中国足球的发展。\\n\\n3. 球员流动性大：中国足球的球员转会制度相对复杂，容易导致球员流动率高。这不仅对俱乐部造成损失，也会影响国家队的战斗力。\\n\\n4. 资源投入不足：虽然近年来中国在足球领域的投资有所增加，但在硬件设施、教练队伍等方面仍有待提高。\\n\\n5. 企业赞助有限：中国企业对足球的关注度并不高，这意味着缺乏足够的资金来支持足球事业的发展。\\n\\n这些因素共同作用，使得中国足球在过去几年中成绩不佳。但是，我们相信随着国家和社会对足球的认可度不断提高，中国足球还有很大的发展空间。')]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["history"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["首先，政府应该加大对足球的投入。中国政府一直以来都十分重视体育事业的发展，而足球作为我国的传统项目之一，更是受到了政府的重点扶持。比如，可以通过制定相关政策，提供财政支持，鼓励企业和社会力量参与到足球项目中来。此外，还可以设立专项基金，用于建设足球训练基地和比赛场地，提升足球场地的硬件设施水平。\n", "\n", "其次，提高足球技术是改善中国足球现状的关键。为了能够引进更多的优秀外援，引进更多的优秀教练，同时也需要加强对青少年足球的培养，培养出一批具有国际竞争力的足球人才。为此，可以在学校开设足球课程，并为参加青少年足球比赛的学生提供一定的奖励，以此来激发他们对足球的兴趣。\n", "\n", "再次，增加青少年足球教育也是至关重要的。青少年是足球发展的未来，他们的足球知识和技术水平直接影响着中国足球的发展。因此，需要加强对青少年足球教育的投入，包括师资培训、教材编写、教学设施配备等方面。同时，也需要注重青少年足球的心理辅导工作，帮助他们克服心理障碍，发挥最佳竞技状态。\n", "\n", "另外，优化转会制度也是改善中国足球现状的重要一环。通过引入更多的外籍教练和优秀的青少年球员，可以提高中国足球的整体技术和战术素养。然而，如果转会制度过于严格，可能会抑制俱乐部的积极性，因此，需要在公平公正的前提下，逐步改革和完善转会制度。\n", "\n", "再者，引导企业参与足球活动，也可以带来新的经济动力，促进足球产业的发展。例如，企业可以投资足球场馆建设、赞助足球赛事等活动，不仅可以获得丰厚的回报，也有利于培养自己的足球人才。\n", "\n", "最后，改进运动员激励机制也是改善中国足球现状的有效途径。通过建立更加公正合理的运动员激励机制，可以吸引更多的优秀人才投身于足球运动，从而提高中国足球的整体实力。\n", "\n", "总的来说，改善中国足球现状需要多方面的努力，包括政策支持、人员培养、技术创新、产业合作等等。只有这样，中国足球才能够在未来取得更大的进步。\n"]}], "source": ["# 第三轮对话 3rd dialogue turn\n", "response, history = model.chat(tokenizer, \"你说的很对，根据你所说的原因，你有什么具体的措施来改善中国足球现状？不少于800字\",\n", "                                history=history,system=\"你是一位专业的足球评论家\")\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 使用`system`参数来调整系统指令，实现角色扮演，语言风格迁移，任务设定，行为设定等能力"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当然可以。考研是一个重要的决定，它可能会对你的生活产生深远的影响。首先，我想告诉你的是，你并不孤单。很多人都有过相似的经历，每个人都有自己的挑战和困难。失眠是许多人都会遇到的问题，你可以尝试一些方法来帮助自己放松，比如进行深呼吸、冥想或者渐进性肌肉松弛等。\n", "\n", "此外，保持积极的心态也很重要。考研的过程可能会充满不确定性，但是你已经做出了这个决定，并且有决心去实现它。同时，也要注意保持健康的生活方式，包括充足的睡眠、健康的饮食和适度的运动。\n", "\n", "最后，如果你觉得压力过大，不妨寻求专业的心理咨询师的帮助。他们可以帮助你理解自己的感受，提供有效的应对策略，以及帮助你调整你的生活方式。\n", "\n", "记住，无论结果如何，你都是一个有价值的人。考研只是一个过程，而你的努力、毅力和坚持才是最重要的。相信自己，你会度过这个难关的。祝你好运！\n"]}], "source": ["response, _ = model.chat(tokenizer, \"我最近在准备考研，压力很大，经常失眠，你能安慰鼓励我一下吗\",\n", "                                history=None,system=\"请你扮演资深的心理咨询师\")\n", "print(response)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["大江东流去，孤城千万峰。\n", "经济学院名，书香溢四海。\n", "桃李满天下，日新月异增。\n", "青楼灯火夜，学子欢歌响。\n"]}], "source": ["response, _ = model.chat(tokenizer, \"请用李白的风格写一首关于‘湖北经济学院’的七言律诗\",history=None)\n", "print(response)"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["这是一个Python程序，使用冒泡排序算法来对一个列表进行排序。这个程序的时间复杂度是O(n^2)，空间复杂度是O(1)。\n", "\n", "```python\n", "def bubble_sort(lst):\n", "    n = len(lst)\n", "    \n", "    for i in range(n):\n", "        for j in range(0, n-i-1):\n", "            if lst[j] > lst[j+1]:\n", "                lst[j], lst[j+1] = lst[j+1], lst[j]\n", "    return lst\n", "\n", "# 测试代码\n", "lst = [64, 34, 25, 12, 22, 11, 90]\n", "print(\"Original list:\", lst)\n", "sorted_lst = bubble_sort(lst)\n", "print(\"Sorted list:\", sorted_lst)\n", "```\n", "\n", "在这个程序中，我们首先获取列表的长度，然后使用两个嵌套的for循环来遍历列表中的每一个元素。如果当前的元素大于下一个元素，我们就交换这两个元素的位置。通过这种方法，我们可以确保每次遍历都会将最大的元素“浮”到列表的最后。\n", "\n", "冒泡排序的时间复杂度是O(n^2)，其中n是列表的长度。这是因为每个元素都需要被比较和交换一次。尽管这看起来是一个时间复杂度较高的算法，但是它在处理大规模数据时仍然是一种有效的解决方案。对于小规模的数据，其他更高效的排序算法（如快速排序或归并排序）可能更适合。然而，在实际应用中，冒泡排序通常是作为一种简单的排序方法，因为它不需要额外的存储空间，并且可以立即得到结果。\n"]}], "source": ["response, history = model.chat(tokenizer, \"请帮我写一个python程序，实现冒泡排序,并给出时间和空间复杂度\",\n", "                                history=None,system='具有15年工具经验的算法工程师')\n", "print(response)"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"text/plain": ["[('请帮我写一个python程序，实现冒泡排序,并给出时间和空间复杂度',\n", "  '这是一个Python程序，使用冒泡排序算法来对一个列表进行排序。这个程序的时间复杂度是O(n^2)，空间复杂度是O(1)。\\n\\n```python\\ndef bubble_sort(lst):\\n    n = len(lst)\\n    \\n    for i in range(n):\\n        for j in range(0, n-i-1):\\n            if lst[j] > lst[j+1]:\\n                lst[j], lst[j+1] = lst[j+1], lst[j]\\n    return lst\\n\\n# 测试代码\\nlst = [64, 34, 25, 12, 22, 11, 90]\\nprint(\"Original list:\", lst)\\nsorted_lst = bubble_sort(lst)\\nprint(\"Sorted list:\", sorted_lst)\\n```\\n\\n在这个程序中，我们首先获取列表的长度，然后使用两个嵌套的for循环来遍历列表中的每一个元素。如果当前的元素大于下一个元素，我们就交换这两个元素的位置。通过这种方法，我们可以确保每次遍历都会将最大的元素“浮”到列表的最后。\\n\\n冒泡排序的时间复杂度是O(n^2)，其中n是列表的长度。这是因为每个元素都需要被比较和交换一次。尽管这看起来是一个时间复杂度较高的算法，但是它在处理大规模数据时仍然是一种有效的解决方案。对于小规模的数据，其他更高效的排序算法（如快速排序或归并排序）可能更适合。然而，在实际应用中，冒泡排序通常是作为一种简单的排序方法，因为它不需要额外的存储空间，并且可以立即得到结果。')]"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["history"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["如果你想要优化冒泡排序的时间复杂度，你可以使用一种称为“分治法”的算法。这是一种将一个问题分解成多个子问题，然后分别解决这些子问题的方法。这样，你就可以直接将原问题的解应用到原问题本身上，从而达到减少时间复杂度的目的。\n", "\n", "以下是使用冒泡排序优化后的代码：\n", "\n", "```python\n", "def bubble_sort_optimized(lst):\n", "    n = len(lst)\n", "    \n", "    # 使用分治法优化冒泡排序\n", "    def merge_sorted_arrays(arr1, arr2):\n", "        result = []\n", "        while arr1 and arr2:\n", "            if arr1[0] < arr2[0]:\n", "                result.append(arr1.pop(0))\n", "            else:\n", "                result.append(arr2.pop(0))\n", "        while arr1:\n", "            result.append(arr1.pop(0))\n", "        while arr2:\n", "            result.append(arr2.pop(0))\n", "        return result\n", "    \n", "    # 将冒泡排序的两层嵌套合并在一起\n", "    sorted_lst = merge_sorted_arrays(bubble_sort(lst), bubble_sort_optimized(lst))\n", "    return sorted_lst\n", "\n", "# 测试代码\n", "lst = [64, 34, 25, 12, 22, 11, 90]\n", "print(\"Original list:\", lst)\n", "sorted_lst = bubble_sort_optimized(lst)\n", "print(\"Sorted list:\", sorted_lst)\n", "```\n", "\n", "在这个优化后的版本中，我们创建了一个名为`merge_sorted_arrays`的辅助函数，该函数接收两个已排序的数组作为输入，并返回一个已排序的新数组。这个函数使用了深度优先搜索（DFS）或广度优先搜索（BFS）等算法来找出两个数组之间的公共元素，然后将它们添加到新的排序数组中。这个过程可以在常数时间内完成，因此总体的时间复杂度是O(n)。虽然这不是最高效的方法，但它至少比原始的冒泡排序要快得多。\n"]}], "source": ["response, history = model.chat(tokenizer, \"请优化上面这段程序，使得时间复杂度降低为O(n)\",history=history,\n", "                               system='具有15年工具经验的算法工程师')\n", "print(response)"]}], "metadata": {"kernelspec": {"display_name": "pytorchl", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}