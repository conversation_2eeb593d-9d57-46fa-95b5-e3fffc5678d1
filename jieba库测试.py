import jieba
import jieba.analyse
import jieba.posseg 
import numpy as np

text_string = '我爱湖北经济学院统计与数学学院大数据2141班'
text_string2 = "华为公司借助云计算平台开发了一款盘古大语言模型"

seg_list1 = jieba.cut(text_string)
seg_list2 = jieba.cut(text_string, cut_all=False, HMM=False)
seg_list3 = jieba.cut(text_string,cut_all=True)

print(list(seg_list1),'\n')
print(list(seg_list2),'\n')
print(list(seg_list3),'\n')

seg_list4 = jieba.lcut(text_string)
print(seg_list4)

seg_list5 = jieba.lcut(text_string2)
print(seg_list5)

jieba.add_word('云计算', freq=3)
seg_list5 = jieba.lcut(text_string2)
print(seg_list5)
jieba.suggest_freq('大语言模型',True)
seg_list5 = jieba.lcut(text_string2)
print(seg_list5)

seg_list6 = jieba.posseg.lcut(text_string2)
print([(word,pos) for word,pos in seg_list6])
"""
下面是jieba工具箱关键词抽取功能
"""
text_string3 = """周杰伦Jay Chou,1979年1月18日出生于台湾省新北市,祖籍福建省泉州市永春县,中国台湾流行乐男歌手、音乐人、演员、导演、编剧，毕业于淡江中学。
2000年发行个人首张专辑《Jay》。2001年发行的专辑《范特西》奠定其融合中西方音乐的风格。2002年举行“The One”世界巡回演唱会 [1] 。2003年成为美国《时代周刊》封面人物 [2] 。2004年发行个人专辑《七里香》 [304] 。2005年凭借动作片《头文字D》获得金马奖、金像奖最佳新人奖 [3] 。2006年起连续三年获得世界音乐大奖中国区最畅销艺人奖 [4] ；同年发行个人专辑《依然范特西》 [305] 。2007年自编自导的文艺片《不能说的秘密》获得金马奖年度台湾杰出电影奖 [5] ；同年发行个人专辑《我很忙》 [306] 。2008年凭借歌曲《青花瓷》获得第19届台湾金曲奖最佳年度歌曲奖、最佳作曲人奖 [307]。
2009年入选美国CNN评出的“25位亚洲最具影响力人物” ;同年凭借专辑《魔杰座》获得第20届台湾金曲奖最佳国语男歌手奖 [7] 。2010年入选美国《Fast Company》评出的“全球百大创意人物”；同年发行个人专辑《跨时代》 [308] ，凭借该专辑获得第22届台湾金曲奖最佳国语男歌手奖、最佳国语专辑奖 [309] 。2012年登上福布斯中国名人榜榜首 [8] 。2014年发行个人首张数字专辑《哎呦，不错哦》 [310] 。2023年凭借专辑《最伟大的作品》获得IFPI全球畅销专辑榜冠军
演艺事业外,他还涉足商业、设计等领域。2007年成立杰威尔有限公司。2011年担任华硕笔电设计师
周杰伦热心公益慈善,多次向中国内地灾区捐款捐物。2008年捐款援建希望小学 """
#基于TFIDF的关键词抽取
extracted_words_tfidf = jieba.analyse.extract_tags(text_string3,topK=10,allowPOS=['n','nr','vn','nt','nw'])
print(extracted_words_tfidf)
extracted_words_textrank = jieba.analyse.textrank(text_string3,topK=10,allowPOS=['n','nr','vn','nt','nw'])
print(extracted_words_textrank)