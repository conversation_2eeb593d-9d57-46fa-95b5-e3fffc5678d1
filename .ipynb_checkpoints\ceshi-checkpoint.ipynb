{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["```python\n", "import matplotlib.pyplot as plt\n", "plt.subplots(2,3)\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 这是我的测试案例代码"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 这也是我的测试案例代码"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 这是我的测试案例代码"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["array([  1.,   2.,   3.,   4.,   5.,   6.,   7.,   8.,   9.,  10.,  11.,\n", "        12.,  13.,  14.,  15.,  16.,  17.,  18.,  19.,  20.,  21.,  22.,\n", "        23.,  24.,  25.,  26.,  27.,  28.,  29.,  30.,  31.,  32.,  33.,\n", "        34.,  35.,  36.,  37.,  38.,  39.,  40.,  41.,  42.,  43.,  44.,\n", "        45.,  46.,  47.,  48.,  49.,  50.,  51.,  52.,  53.,  54.,  55.,\n", "        56.,  57.,  58.,  59.,  60.,  61.,  62.,  63.,  64.,  65.,  66.,\n", "        67.,  68.,  69.,  70.,  71.,  72.,  73.,  74.,  75.,  76.,  77.,\n", "        78.,  79.,  80.,  81.,  82.,  83.,  84.,  85.,  86.,  87.,  88.,\n", "        89.,  90.,  91.,  92.,  93.,  94.,  95.,  96.,  97.,  98.,  99.,\n", "       100.])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linspace(1,100,100)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import jieba"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 这是我的另外一个测试代码"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["<generator object Tokenizer.cut at 0x0000023810884C80>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["seg_results = jieba.cut('我来自湖北武汉市洪山区保利蓝海郡')\n", "seg_results"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["我\n", "来自\n", "湖北\n", "武汉市\n", "洪山区\n", "保利\n", "蓝海\n", "郡\n"]}], "source": ["for item in seg_results:\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["<matplotlib.collections.PathCollection at 0x2ba28a818d0>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Program Files\\anaconda3\\lib\\site-packages\\IPython\\core\\events.py:89: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from current font.\n", "  func(*args, **kwargs)\n", "d:\\Program Files\\anaconda3\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 900x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "\n", "\n", "x_values = np.random.randint(1,100,20)\n", "y_values = np.random.randint(1,50,20)\n", "\n", "fig = plt.figure(figsize=(9,6))\n", "axes = fig.add_axes([0,0,1,1])\n", "axes.set_xlabel('x值')\n", "axes.set_ylabel('y值')\n", "axes.scatter(x_values,y_values)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "fig = plt.figure(figsize=(11,8))\n", "ax = fig.add_axes([0,0,1,1])\n", "ax.bar()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 2}