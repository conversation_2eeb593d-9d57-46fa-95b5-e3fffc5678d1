{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### 中文的BERT模型`bert-base-chinese`进行了掩码语言模型`（Masked Language Model, MLM）`的预测\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**安装好pytroch和transformers两个工具库**<br/>\n", "导入所需的`PyTorch`和`transformers`库中的`AutoTokenizer`和`AutoModelForMaskedLM`模块。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "from transformers import AutoTokenizer, AutoModelForMaskedLM"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1. 使用`bert-base-chines`预训练模型的tokenizer进行文本的分词和编码。\n", "2. 使用`bert-base-chinese`预训练模型的Masked Language Model进行加载，以便后续用于生成对被[MASK]掩码的标记的预测。\n", "    - 如果是第一次加载模型，会从huggingface face模型库中下载该模型的参数文件和tokenizer。如果模型很大，可能需要较长时间\n", "    - 模型下载后会默认保存在`C:\\Users\\<USER>\\.cache\\huggingface`文件夹当中，可以通过设置环境变量来修改默认缓存文件夹\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Some weights of the model checkpoint at bert-base-chinese were not used when initializing BertForMaskedLM: ['cls.seq_relationship.bias', 'bert.pooler.dense.bias', 'cls.seq_relationship.weight', 'bert.pooler.dense.weight']\n", "- This IS expected if you are initializing BertForMaskedLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).\n", "- This IS NOT expected if you are initializing BertForMaskedLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).\n"]}, {"data": {"text/plain": ["BertTokenizerFast(name_or_path='bert-base-chinese', vocab_size=21128, model_max_length=512, is_fast=True, padding_side='right', truncation_side='right', special_tokens={'unk_token': '[UNK]', 'sep_token': '[SEP]', 'pad_token': '[PAD]', 'cls_token': '[CLS]', 'mask_token': '[MASK]'}, clean_up_tokenization_spaces=True),  added_tokens_decoder={\n", "\t0: AddedToken(\"[PAD]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "\t100: AddedToken(\"[UNK]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "\t101: AddedToken(\"[CLS]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "\t102: AddedToken(\"[SEP]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "\t103: AddedToken(\"[MASK]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer = AutoTokenizer.from_pretrained(\"bert-base-chinese\")\n", "model = AutoModelForMaskedLM.from_pretrained(\"bert-base-chinese\")\n", "tokenizer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 给定一个包含`[MASK]`的文本字符串，该文本将被用于模型的输入\n", "- 使用tokenizer对文本进行编码，并返回PyTorch张量格式的模型输入。`return_tensors='pt'`参数指定返回PyTorch张量"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input_ids': tensor([[ 101, 2548, 3255,  860, 5401, 1227, 3221, 2190,  103, 4638, 5162, 6574,\n", "         2137,  855, 4638, 1825, 3315, 1114, 1156, 8024,  738, 3221, 1059,  686,\n", "         4518,  782, 5102, 4852,  833, 3136, 5509, 4638, 6633, 1403, 4680, 3403,\n", "          511,  102]]), 'token_type_ids': tensor([[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "         0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]), 'attention_mask': tensor([[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "         1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]])}"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["text =\"德智体美劳是对[MASK]的素质定位的基本准则，也是全世界人类社会教育的趋向目标。\"\n", "# text = \"[MASK]国的首都是北京。\"\n", "# text = \"[MASK]国的首都是华盛顿。\"\n", "inputs = tokenizer(text,return_tensors='pt',padding=True)\n", "inputs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["找到文本中`[MASK]`标记对应的索引位置。`tokenizer.mask_token_id`是特殊标记`[MASK]`在模型中的编码。"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([8])"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["mask_token_index = torch.where(inputs[\"input_ids\"] == tokenizer.mask_token_id)[1]\n", "mask_token_index"]}, {"cell_type": "markdown", "metadata": {}, "source": ["通过将输入传递给预训练的MLM模型，获取模型的预测`logits`。"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([1, 38, 21128])"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["logits = model(**inputs).logits\n", "logits.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["从logits中提取`[MASK]`标记的预测概率。"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Size([1, 21128])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["mask_token_logits = logits[0,mask_token_index,:]\n", "mask_token_logits.shape"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.return_types.topk(\n", "values=tensor([[19.2418, 12.2560, 11.8999, 11.6043, 11.0461, 10.7895, 10.7880, 10.1795,\n", "         10.0705, 10.0247]], grad_fn=<TopkBackward0>),\n", "indices=tensor([[ 782, 4495, 2110, 6716, 2595, 2769, 4868, 1054, 5401, 3413]]))"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.topk(mask_token_logits, 10, dim=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["找到概率最高的前10个标记的索引，将其转换为Python列表。这里使用了`torch.topk`函数。"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["[782, 4495, 2110, 6716, 2595, 2769, 4868, 1054, 5401, 3413]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["top_10_tokens = torch.topk(mask_token_logits, 10, dim=1).indices[0].tolist()\n", "top_10_tokens"]}, {"cell_type": "markdown", "metadata": {}, "source": ["针对每一个前十个预测标记，替换原始文本中的`[MASK]`标记，并打印出替换后的文本。`tokenizer.decode([token])`用于将模型输出的标记索引解码为文本。"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["排名第1的MASK预测： 德智体美劳是对[人]的素质定位的基本准则，也是全世界人类社会教育的趋向目标。\n", "排名第2的MASK预测： 德智体美劳是对[生]的素质定位的基本准则，也是全世界人类社会教育的趋向目标。\n", "排名第3的MASK预测： 德智体美劳是对[学]的素质定位的基本准则，也是全世界人类社会教育的趋向目标。\n", "排名第4的MASK预测： 德智体美劳是对[身]的素质定位的基本准则，也是全世界人类社会教育的趋向目标。\n", "排名第5的MASK预测： 德智体美劳是对[性]的素质定位的基本准则，也是全世界人类社会教育的趋向目标。\n", "排名第6的MASK预测： 德智体美劳是对[我]的素质定位的基本准则，也是全世界人类社会教育的趋向目标。\n", "排名第7的MASK预测： 德智体美劳是对[神]的素质定位的基本准则，也是全世界人类社会教育的趋向目标。\n", "排名第8的MASK预测： 德智体美劳是对[党]的素质定位的基本准则，也是全世界人类社会教育的趋向目标。\n", "排名第9的MASK预测： 德智体美劳是对[美]的素质定位的基本准则，也是全世界人类社会教育的趋向目标。\n", "排名第10的MASK预测： 德智体美劳是对[校]的素质定位的基本准则，也是全世界人类社会教育的趋向目标。\n"]}], "source": ["\n", "top_10_tokens = torch.topk(mask_token_logits, 10, dim=1).indices[0].tolist()\n", "for index, token in enumerate(top_10_tokens):\n", "    print(f'排名第{index+1}的MASK预测：', text.replace(tokenizer.mask_token, f'[{tokenizer.decode([token])}]'))"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1000: '傲', 1002: '債', 1004: '傻', 1006: '僅', 1008: '像', 1010: '僕', 1012: '僚', 1014: '僧', 1016: '僮', 1018: '僵', 1020: '僻', 1022: '儂', 1024: '儆', 1026: '儋', 1028: '儕', 1030: '償', 1032: '優', 1034: '儷', 1036: '儿', 1038: '允', 1040: '兄', 1042: '兆', 1044: '先', 1046: '克', 1048: '免', 1050: '兑', 1052: '兔', 1054: '党', 1056: '兢', 1058: '內', 1060: '兩', 1062: '公', 1064: '兮', 1066: '共', 1068: '关', 1070: '兵', 1072: '具', 1074: '兹', 1076: '兼', 1078: '冀', 1080: '円', 1082: '冈', 1084: '冊', 1086: '再', 1088: '冒', 1090: '冗', 1092: '军', 1094: '冠', 1096: '冤', 1098: '冨', 1100: '冬', 1102: '冰', 1104: '决', 1106: '冶', 1108: '冻', 1110: '冽', 1112: '净', 1114: '准', 1116: '凈', 1118: '凋', 1120: '凍', 1122: '凑', 1124: '凜', 1126: '几', 1128: '凤', 1130: '凪', 1132: '凯', 1134: '凱', 1136: '凶', 1138: '凹', 1140: '击', 1142: '凿', 1144: '刁', 1146: '分', 1148: '刈', 1150: '刍', 1152: '刑', 1154: '列', 1156: '则', 1158: '创', 1160: '删', 1162: '別', 1164: '利', 1166: '别', 1168: '到', 1170: '刷', 1172: '刹', 1174: '刻', 1176: '剁', 1178: '剃', 1180: '剉', 1182: '剋', 1184: '前', 1186: '剐', 1188: '剔', 1190: '剛', 1192: '剝', 1194: '剤', 1196: '剧', 1198: '剪', 1200: '割', 1202: '剷', 1204: '剿', 1206: '劇', 1208: '劉', 1210: '劍', 1212: '劑', 1214: '劝', 1216: '功', 1218: '务', 1220: '动', 1222: '努', 1224: '劭', 1226: '劲', 1228: '労', 1230: '効', 1232: '势', 1234: '勃', 1236: '勉', 1238: '勐', 1240: '動', 1242: '勘', 1244: '勛', 1246: '勞', 1248: '勢', 1250: '勧', 1252: '勵', 1254: '勺', 1256: '勾', 1258: '匀', 1260: '匆', 1262: '匍', 1264: '匕', 1266: '北', 1268: '匝', 1270: '匡', 1272: '匪', 1274: '匯', 1276: '匹', 1278: '医', 1280: '匿', 1282: '十', 1284: '卅', 1286: '午', 1288: '半', 1290: '华', 1292: '卑', 1294: '卓', 1296: '单', 1298: '南', 1300: '博', 1302: '卞', 1304: '占', 1306: '卢', 1308: '卦', 1310: '卫', 1312: '卯', 1314: '危', 1316: '却', 1318: '卷', 1320: '卻', 1322: '厂', 1324: '厅', 1326: '厉', 1328: '厌', 1330: '厘', 1332: '厝', 1334: '厢', 1336: '厦', 1338: '厩', 1340: '厮', 1342: '厳', 1344: '县', 1346: '参', 1348: '又', 1350: '及', 1352: '双', 1354: '収', 1356: '叔', 1358: '受', 1360: '叙', 1362: '叟', 1364: '叡', 1366: '口', 1368: '句', 1370: '叨', 1372: '只', 1374: '召', 1376: '叮', 1378: '台', 1380: '史', 1382: '叵', 1384: '号', 1386: '叹', 1388: '叼', 1390: '吁', 1392: '各', 1394: '合', 1396: '吊', 1398: '同', 1400: '后', 1402: '吐', 1404: '吒', 1406: '吕', 1408: '吗', 1410: '吝', 1412: '吟', 1414: '吡', 1416: '吧', 1418: '吩', 1420: '听', 1422: '吮', 1424: '吱', 1426: '吴', 1428: '吶', 1430: '吹', 1432: '吼', 1434: '吾', 1436: '呂', 1438: '呆', 1440: '告', 1442: '呎', 1444: '呓', 1446: '呗', 1448: '呛', 1450: '呢', 1452: '呦', 1454: '呱', 1456: '味', 1458: '呷', 1460: '呻', 1462: '命', 1464: '咁', 1466: '咄', 1468: '咋', 1470: '咎', 1472: '咐', 1474: '咔', 1476: '咖', 1478: '咘', 1480: '咚', 1482: '咣', 1484: '咦', 1486: '咨', 1488: '咪', 1490: '咬', 1492: '咯', 1494: '咲', 1496: '咸', 1498: '咽', 1500: '哀', 1502: '哂', 1504: '哆', 1506: '哈', 1508: '哋', 1510: '响', 1512: '哏', 1514: '哑', 1516: '哔', 1518: '哟', 1520: '哥', 1522: '哧', 1524: '哩', 1526: '哭', 1528: '哲', 1530: '哼', 1532: '唁', 1534: '唆', 1536: '唉', 1538: '唐', 1540: '唔', 1542: '唤', 1544: '唬', 1546: '唯', 1548: '唱', 1550: '唷', 1552: '唾', 1554: '啄', 1556: '啉', 1558: '問', 1560: '啕', 1562: '啜', 1564: '啟', 1566: '啤', 1568: '啦', 1570: '啪', 1572: '啬', 1574: '啰', 1576: '啲', 1578: '啶', 1580: '啸', 1582: '啼', 1584: '喀', 1586: '喃', 1588: '喆', 1590: '喉', 1592: '喋', 1594: '喏', 1596: '喘', 1598: '喚', 1600: '喝', 1602: '喧', 1604: '喫', 1606: '單', 1608: '喱', 1610: '喳', 1612: '営', 1614: '喹', 1616: '喻', 1618: '嗅', 1620: '嗇', 1622: '嗑', 1624: '嗓', 1626: '嗖', 1628: '嗜', 1630: '嗟', 1632: '嗣', 1634: '嗦', 1636: '嗪', 1638: '嗯', 1640: '嗲', 1642: '嗶', 1644: '嗽', 1646: '嘅', 1648: '嘈', 1650: '嘌', 1652: '嘎', 1654: '嘖', 1656: '嘘', 1658: '嘛', 1660: '嘞', 1662: '嘢', 1664: '嘤', 1666: '嘩', 1668: '嘮', 1670: '嘰', 1672: '嘲', 1674: '嘶', 1676: '嘹', 1678: '嘿', 1680: '噌', 1682: '噓', 1684: '噗', 1686: '噜', 1688: '噢', 1690: '器', 1692: '噪', 1694: '噱', 1696: '噶', 1698: '噹', 1700: '噼', 1702: '嚇', 1704: '嚏', 1706: '嚓', 1708: '嚟', 1710: '嚥', 1712: '嚮', 1714: '嚷', 1716: '囂', 1718: '囊', 1720: '囑', 1722: '囗', 1724: '四', 1726: '回', 1728: '因', 1730: '团', 1732: '囤', 1734: '囪', 1736: '园', 1738: '囱', 1740: '図', 1742: '囹', 1744: '国', 1746: '囿', 1748: '圄', 1750: '圈', 1752: '圍', 1754: '園', 1756: '圖', 1758: '圜', 1760: '圣', 1762: '在', 1764: '圭', 1766: '圳', 1768: '圻', 1770: '址', 1772: '均', 1774: '坍', 1776: '坏', 1778: '坑', 1780: '坚', 1782: '坝', 1784: '坟', 1786: '坡', 1788: '坦', 1790: '坪', 1792: '坳', 1794: '坷', 1796: '垃', 1798: '型', 1800: '垚', 1802: '垠', 1804: '垣', 1806: '垩', 1808: '垭', 1810: '垵', 1812: '埃', 1814: '城', 1816: '埕', 1818: '域', 1820: '埤', 1822: '執', 1824: '培', 1826: '埼', 1828: '堂', 1830: '堅', 1832: '堇', 1834: '堕', 1836: '堡', 1838: '堪', 1840: '堰', 1842: '場', 1844: '堺', 1846: '塊', 1848: '塑', 1850: '塗', 1852: '塚', 1854: '塢', 1856: '填', 1858: '塭', 1860: '塾', 1862: '境', 1864: '墉', 1866: '墒', 1868: '増', 1870: '墙', 1872: '增', 1874: '墨', 1876: '墮', 1878: '墻', 1880: '壁', 1882: '壆', 1884: '壊', 1886: '壓', 1888: '壘', 1890: '壟', 1892: '壤', 1894: '士', 1896: '壮', 1898: '声', 1900: '壳', 1902: '壹', 1904: '壽', 1906: '备', 1908: '复', 1910: '夔', 1912: '外', 1914: '多', 1916: '够', 1918: '夢', 1920: '大', 1922: '太', 1924: '夭', 1926: '夯', 1928: '头', 1930: '夸', 1932: '夺', 1934: '奂', 1936: '奇', 1938: '奉', 1940: '奎', 1942: '奐', 1944: '奔', 1946: '奖', 1948: '奘', 1950: '奠', 1952: '奥', 1954: '奪', 1956: '奮', 1958: '奴', 1960: '奸', 1962: '好', 1964: '妃', 1966: '妆', 1968: '妈', 1970: '妍', 1972: '妓', 1974: '妘', 1976: '妝', 1978: '妣', 1980: '妥', 1982: '妩', 1984: '妮', 1986: '妳', 1988: '妻', 1990: '姆', 1992: '姊', 1994: '姍', 1996: '姑', 1998: '姓'}\n"]}], "source": ["\n", "print({i:tokenizer.decode([i]) for i in range(1000,2000,2)})"]}], "metadata": {"kernelspec": {"display_name": "pytorchl", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}