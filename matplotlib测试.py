import matplotlib.pyplot as plt
import numpy as np
import pandas as pd


# x_values = np.random.randint(1,100,20)
# y_values = np.random.randint(1,50,20)

# fig = plt.figure(figsize=(9,6))
# axes = fig.add_subplot(1,1,1)
# axes.set_xlabel('x_values')
# axes.set_ylabel('y_values')
# axes.set_title('scatter plot')
# axes.scatter(x_values,y_values,c=[(0.7,0.3,0.7)]*20,s=np.random.randint(0,200,20))
# plt.show()


# plt.scatter(x_values,y_values)
# plt.show()

t = np.linspace(0, 5*np.pi, 1000)
x = t * np.sin(t)
y = t * np.cos(t)

fig_1, axes_1 = plt.subplots(2,3,figsize=(12,8))
for i,ax in enumerate(axes_1.flatten()):
    ax.plot(x, (i+1)*y, 'm--', linewidth=2)
    ax.set_title('The Spirals Curves Function of $x^2+y^2=t^2$')

plt.tight_layout()


fig_2 = plt.figure(figsize=(12,8))
ax_1 = fig_2.add_subplot(2,3,1,projection='polar')
ax_1.plot(t, np.sin(4*t))
ax_2 = fig_2.add_subplot(2,3,2,projection='polar')
ax_2.plot(t, np.sin(5*t))
ax_3 = fig_2.add_subplot(2,3,3)
ax_3.plot(t, np.sin(6*t))
ax_4 = fig_2.add_subplot(2,3,4,projection='polar')
ax_4.plot(t, np.sin(1/5*t))
plt.tight_layout()

t = np.linspace(0,2*np.pi,100)
radius = 3*np.random.rand(100)
x = radius*np.sin(t)+1
y = radius*np.cos(t)+1
# colors = np.random.rand(100)
fig_3, axe_3 = plt.subplots(figsize=(8,8))
axe_3.scatter(x,y,s=500,c='y',alpha=0.5)
axe_3.scatter(x+5,y+5,s=500, c='r', marker='^',alpha=0.3)

province_list = ['guangzhou','wuhan','hangzhou','beijing','shanghai']
gdp_arr = [[3000,2300,2600,5600,5200],[2900,2600,2700,4900,4800]]
fig_bar, axe_bar = plt.subplots(figsize=(12,6))
axe_bar.bar(np.arange(5), gdp_arr[0],width=0.3,label='June')
axe_bar.bar(np.arange(5)+0.3, gdp_arr[1],width=0.3,label='July')
axe_bar.set_xticks(np.arange(5)+0.15, province_list)
axe_bar.set_title('The GDP comparison of Five first-class cities')
axe_bar.legend(loc='center left',bbox_to_anchor=(0,0.95), ncols=2)
for i in range(5):
    axe_bar.text(i, gdp_arr[0][i], str(gdp_arr[0][i]), ha='center', va='bottom')
    axe_bar.text(i+0.3, gdp_arr[1][i], str(gdp_arr[1][i]), ha='center', va='bottom')

plt.show()
