seaborn-data
============

Data repository for [seaborn](http://seaborn.pydata.org/) examples.

| :warning: This is not a general-purpose data archive :warning: |
| :---: |

This repository exists only to provide a convenient target for the `seaborn.load_dataset` function to download sample datasets from. Its existence makes it easy to document seaborn without confusing things by spending time loading and munging data. The datasets may change or be removed at any time if they are no longer useful for the seaborn documentation. Some of the datasets have also been modifed from their canonical sources.

Data sources
------------

A partial list of where these datasets originate from.

- `anagrams`: https://psych252.github.io/
- `anscombe`: https://en.wikipedia.org/wiki/Anscombe%27s_quartet
- `attention`: https://psych252.github.io/
- `car_crashes`: https://www.kaggle.com/fivethirtyeight/fivethirtyeight-bad-drivers-dataset
- `diamonds`: https://ggplot2.tidyverse.org/reference/diamonds.html
- `dots`: https://shadlenlab.columbia.edu/resources/RoitmanDataCode.html
- `dowjones`: https://fred.stlouisfed.org/series/M1109BUSM293NNBR
- `exercise`: https://psych252.github.io
- `fmri`: https://github.com/mwaskom/Waskom_CerebCortex_2017
- `geyser`: https://stat.ethz.ch/R-manual/R-devel/library/datasets/html/faithful.html
- `glue`: https://gluebenchmark.com/leaderboard
- `healthexp`: https://ourworldindata.org/grapher/life-expectancy-vs-health-expenditure
- `iris`: https://archive.ics.uci.edu/ml/datasets/iris
- `mpg`: https://data.world/dataman-udit/cars-data
- `penguins`: https://github.com/allisonhorst/penguins
- `planets`: https://exoplanets.nasa.gov/exoplanet-catalog/
- `seaice`: https://nsidc.org/arcticseaicenews/sea-ice-tools/
- `taxis`:  https://www1.nyc.gov/site/tlc/about/tlc-trip-record-data.page
- `tips`: https://rdrr.io/cran/reshape2/man/tips.html
- `titanic`: https://www.kaggle.com/c/titanic/data
