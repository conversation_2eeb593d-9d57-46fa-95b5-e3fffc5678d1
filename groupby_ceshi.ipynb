{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>isVip</th>\n", "      <th>gender</th>\n", "      <th>age</th>\n", "      <th>vipLevel</th>\n", "      <th>growValue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>vip</td>\n", "      <td>male</td>\n", "      <td>25</td>\n", "      <td>LV2</td>\n", "      <td>180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>svip</td>\n", "      <td>female</td>\n", "      <td>30</td>\n", "      <td>LV5</td>\n", "      <td>425</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>member</td>\n", "      <td>female</td>\n", "      <td>40</td>\n", "      <td>LV3</td>\n", "      <td>380</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>vip</td>\n", "      <td>female</td>\n", "      <td>25</td>\n", "      <td>LV3</td>\n", "      <td>288</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>member</td>\n", "      <td>male</td>\n", "      <td>40</td>\n", "      <td>LV2</td>\n", "      <td>190</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>vip</td>\n", "      <td>female</td>\n", "      <td>18</td>\n", "      <td>LV2</td>\n", "      <td>110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>svip</td>\n", "      <td>male</td>\n", "      <td>30</td>\n", "      <td>LV3</td>\n", "      <td>240</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    isVip  gender  age vipLevel  growValue\n", "0     vip    male   25      LV2        180\n", "1    svip  female   30      LV5        425\n", "2  member  female   40      LV3        380\n", "3     vip  female   25      LV3        288\n", "4  member    male   40      LV2        190\n", "5     vip  female   18      LV2        110\n", "6    svip    male   30      LV3        240"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "vip_df = pd.DataFrame(\n", "    {'isVip': ['vip', 'svip', 'member', 'vip', 'member', 'vip', 'svip'],\n", "     'gender': ['male', 'female', 'female', 'female', 'male', 'female', 'male'],\n", "     'age': [25, 30, 40, 25, 40, 18, 30],\n", "     'vipLevel': ['LV2', 'LV5', 'LV3', 'LV3', 'LV2', 'LV2', 'LV3'],\n", "     'growValue': [180, 425, 380, 288, 190, 110, 240]}\n", ")\n", "vip_df"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["('female',\n", "    growValue  age\n", " 1        425   30\n", " 2        380   40\n", " 3        288   25\n", " 5        110   18)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["vip_df1 = vip_df.groupby(by='gender')[['growValue','age']]\n", "group_list = [i for i in vip_df1]\n", "group_list[0]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>isVip</th>\n", "      <th>age</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>vip</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>svip</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>member</td>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>vip</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>member</td>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>vip</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>svip</td>\n", "      <td>30</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    isVip  age\n", "0     vip   25\n", "1    svip   30\n", "2  member   40\n", "3     vip   25\n", "4  member   40\n", "5     vip   18\n", "6    svip   30"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["vip_df[['isVip','age']]"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.761659</td>\n", "      <td>0.502653</td>\n", "      <td>0.846016</td>\n", "      <td>0.709817</td>\n", "      <td>0.570766</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.829733</td>\n", "      <td>0.971366</td>\n", "      <td>0.430031</td>\n", "      <td>0.975225</td>\n", "      <td>0.359185</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.946190</td>\n", "      <td>0.101804</td>\n", "      <td>0.357040</td>\n", "      <td>0.464494</td>\n", "      <td>0.022804</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.335473</td>\n", "      <td>0.742418</td>\n", "      <td>0.317193</td>\n", "      <td>0.319403</td>\n", "      <td>0.905111</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          0         1         2         3         4\n", "0  0.761659  0.502653  0.846016  0.709817  0.570766\n", "1  0.829733  0.971366  0.430031  0.975225  0.359185\n", "2  0.946190  0.101804  0.357040  0.464494  0.022804\n", "3  0.335473  0.742418  0.317193  0.319403  0.905111"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(np.random.rand(4,5))"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>10</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>12</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>14</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>16</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>18</td>\n", "      <td>19</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    A   B\n", "0   0   1\n", "1   2   3\n", "2   4   5\n", "3   6   7\n", "4   8   9\n", "5  10  11\n", "6  12  13\n", "7  14  15\n", "8  16  17\n", "9  18  19"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.preprocessing import MinMaxScaler\n", "\n", "scaler = MinMaxScaler((0,1))\n", "df = pd.DataFrame(np.arange(20).reshape(10,2),columns=['A','B'])\n", "df"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-1.        , -1.        ],\n", "       [-0.77777778, -0.77777778],\n", "       [-0.55555556, -0.55555556],\n", "       [-0.33333333, -0.33333333],\n", "       [-0.11111111, -0.11111111],\n", "       [ 0.11111111,  0.11111111],\n", "       [ 0.33333333,  0.33333333],\n", "       [ 0.55555556,  0.55555556],\n", "       [ 0.77777778,  0.77777778],\n", "       [ 1.        ,  1.        ]])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["scaler.fit_transform(df)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-1.000000</td>\n", "      <td>-1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-0.777778</td>\n", "      <td>-0.777778</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-0.555556</td>\n", "      <td>-0.555556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-0.333333</td>\n", "      <td>-0.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-0.111111</td>\n", "      <td>-0.111111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.111111</td>\n", "      <td>0.111111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0.333333</td>\n", "      <td>0.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>0.555556</td>\n", "      <td>0.555556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0.777778</td>\n", "      <td>0.777778</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          A         B\n", "0 -1.000000 -1.000000\n", "1 -0.777778 -0.777778\n", "2 -0.555556 -0.555556\n", "3 -0.333333 -0.333333\n", "4 -0.111111 -0.111111\n", "5  0.111111  0.111111\n", "6  0.333333  0.333333\n", "7  0.555556  0.555556\n", "8  0.777778  0.777778\n", "9  1.000000  1.000000"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["def func_scaler(x,min_,max_):\n", "    scaled_x = (x-x.min())/(x.max()-x.min())\n", "    scaled_x = scaled_x * (max_ - min_) + min_ \n", "    return scaled_x\n", "\n", "df.apply(func_scaler, args=(-1,1))"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["\n", "def softmax(x):\n", "    # 计算指数值\n", "    exp_x = np.exp(x - np.max(x))  # 减去最大值以防止数值不稳定性\n", "\n", "    # 计算分母\n", "    sum_exp_x = np.sum(exp_x,axis=1,keepdims=True)\n", "    print(sum_exp_x)\n", "\n", "    # 计算softmax\n", "    softmax_x = exp_x / sum_exp_x\n", "\n", "    return softmax_x"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1.55300179]\n", " [0.539846  ]]\n", "[[0.0320586  0.08714432 0.23688282 0.64391426]\n", " [0.03392753 0.25069239 0.03392753 0.68145256]]\n"]}], "source": ["arr = np.array([[2,3,4,5],[1,3,1,4]])\n", "print(softmax(arr))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}