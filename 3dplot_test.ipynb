{"cells": [{"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using matplotlib backend: Qt5Agg\n"]}, {"data": {"text/plain": ["<matplotlib.colorbar.Colorbar at 0x1efef3b23e0>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["%matplotlib \n", "z = np.linspace(0,3,endpoint=True)\n", "x = z * np.sin(6*z)\n", "y = z * np.cos(6*z)\n", "\n", "fig, ax = plt.subplots(figsize=(10,10),subplot_kw={'projection':'3d'})\n", "ax1 = ax.scatter(x,y,z,s=200, c= z, cmap='afmhot_r',alpha=1)\n", "ax.plot(x,y,z,color='orange',linestyle=':')\n", "fig.colorbar(ax1,shrink=0.3,extend='max')\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.colorbar.Colorbar at 0x1efe9ec6380>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["x = np.linspace(-6,6,100)\n", "y = np.linspace(-6,6,100)\n", "x, y = np.meshgrid(x,y)\n", "z = np.sin(np.sqrt(x**2+y**2))\n", "\n", "fig = plt.figure(figsize=(10,10))\n", "ax = fig.add_subplot(111,projection='3d')\n", "surface = ax.plot_surface(x,y,z,cmap='coolwarm')\n", "contour = ax.contour(x,y,z,levels=np.arange(-1,1,0.5),zdir='z',offset=-2,cmap='coolwarm')\n", "ax.set_zlim([-2,2])\n", "fig.colorbar(surface,shrink=0.5,pad=0.03)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["<mpl_toolkits.mplot3d.art3d.Poly3DCollection at 0x1eff77b3220>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["%matplotlib qt\n", "theta = np.linspace(0,2*np.pi,30)[:,np.newaxis]\n", "r = np.linspace(0,1,30)\n", "x = (r*np.sin(theta)).flatten()\n", "y = (r*np.cos(theta)).flatten()\n", "z = np.sin(-x*y)\n", "\n", "fig = plt.figure(figsize=(10,10))\n", "ax = fig.add_subplot(1,1,1,projection='3d')\n", "ax.plot_trisurf(x,y,z,cmap='PuBu_r')\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["import requests\n", "import parsel"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["200\n", "utf-8\n", "utf-8\n", "<!DOCTYPE html>\n", "<html lang=\"zh-CN\" class=\"ua-windows ua-webkit\">\n", "<head>\n", "    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n", "    <meta name=\"renderer\" content=\"webkit\">\n", "    <meta name=\"referrer\" content=\"always\">\n", "    <meta name=\"google-site-verification\" content=\"ok0wCgT20tBBgo9_zat2iAcimtN4Ftf5ccsh092Xeyw\" />\n", "    <title>\n", "        坚如磐石 (豆瓣)\n", "</title>\n", "    \n", "    <meta name=\"baidu-site-verification\" content=\"cZdR4xxR7RxmM4zE\" />\n", "    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n", "    <meta http-equiv=\"Expires\" content=\"Sun, 6 Mar 2006 01:00:00 GMT\">\n", "    \n", "    <link rel=\"apple-touch-icon\" href=\"https://img1.doubanio.com/f/movie/d59b2715fdea4968a450ee5f6c95c7d7a2030065/pics/movie/apple-touch-icon.png\">\n", "    <link href=\"https://img1.doubanio.com/f/vendors/02814fbb5bee25484516bd0a642af695f7ec5a83/css/douban.css\" rel=\"stylesheet\" type=\"text/css\">\n", "    <link href=\"https://img1.doubanio.com/f/vendors/ee6598d46af0bc554cecec9bcbf525b9b0582cb0/css/separation/_all.css\" rel=\"stylesheet\" type=\"text/css\">\n", "    <link href=\"https://img1.doubanio.com/f/movie/cb1cb6aaa244dff6a281d103ff26d445debd130a/dist/movie/base/init.css\" rel=\"stylesheet\">\n", "    <script type=\"text/javascript\">var _head_start = new Date();</script>\n", "    <script type=\"text/javascript\" src=\"https://img1.doubanio.com/f/vendors/0511abe9863c2ea7084efa7e24d1d86c5b3974f1/js/jquery-1.10.2.min.js\"></script>\n", "    <script type=\"text/javascript\" src=\"https://img1.doubanio.com/f/vendors/aa9559674f2476cdc16f755b3cdc4ebc478db669/js/douban.js\"></script>\n", "    <script type=\"text/javascript\" src=\"https://img1.doubanio.com/f/vendors/b0d3faaf7a432605add54908e39e17746824d6cc/js/separation/_all.js\"></script>\n", "    \n", "    <meta name=\"keywords\" content=\"坚如磐石,坚如磐石,坚如磐石影评,剧情介绍,电影图片,预告片,影讯,在线购票,论坛\">\n", "    <meta name=\"description\" content=\"坚如磐石电影简介和剧情介绍,坚如磐石影评、图片、预告片、影讯、论坛、在线购票\">\n", "    <meta name=\"mobile-agent\" content=\"format=html5; url=https://m.douban.com/movie/subject/********/\"/>\n", "    <link rel=\"alternate\" href=\"android-app://com.douban.movie/doubanmovie/subject/********/\" />\n", "    <link rel=\"stylesheet\" href=\"https://img1.doubanio.com/dae/cdnlib/libs/LikeButton/1.0.5/style.min.css\">\n", "    <link rel=\"stylesheet\" href=\"https://img1.doubanio.com/f/vendors/0035bb2f83e2cba49ecf634fed57f9ff1bbd0d09/css/ui/dialog.css\">\n", "    <link rel=\"stylesheet\" href=\"https://img1.doubanio.com/f/movie/ea46c1bbb13fe495ccb639fd815b628ba32df4c5/dist/movie/common/show.css\">\n", "    \n", "  <script type='text/javascript'>\n", "  var _vwo_code = (function() {\n", "    var account_id = 249272,\n", "      settings_tolerance = 0,\n", "      library_tolerance = 2500,\n", "      use_existing_jquery = false,\n", "      // DO NOT EDIT BELOW THIS LINE\n", "      f=false,d=document;return{use_existing_jquery:function(){return use_existing_jquery;},library_tolerance:function(){return library_tolerance;},finish:function(){if(!f){f=true;var a=d.getElementById('_vis_opt_path_hides');if(a)a.parentNode.removeChild(a);}},finished:function(){return f;},load:function(a){var b=d.createElement('script');b.src=a;b.type='text/javascript';b.innerText;b.onerror=function(){_vwo_code.finish();};d.getElementsByTagName('head')[0].appendChild(b);},init:function(){settings_timer=setTimeout('_vwo_code.finish()',settings_tolerance);var a=d.createElement('style'),b='body{opacity:0 !important;filter:alpha(opacity=0) !important;background:none !important;}',h=d.getElementsByTagName('head')[0];a.setAttribute('id','_vis_opt_path_hides');a.setAttribute('type','text/css');if(a.styleSheet)a.styleSheet.cssText=b;else a.appendChild(d.createTextNode(b));h.appendChild(a);this.load('//dev.visualwebsiteoptimizer.com/j.php?a='+account_id+'&u='+encodeURIComponent(d.URL)+'&r='+Math.random());return settings_timer;}};}());\n", "\n", "  +function () {\n", "    var bindEvent = function (el, type, handler) {\n", "        var $ = window.jQuery || window.Zepto || window.$\n", "       if ($ && $.fn && $.fn.on) {\n", "           $(el).on(type, handler)\n", "       } else if($ && $.fn && $.fn.bind) {\n", "           $(el).bind(type, handler)\n", "       } else if (el.addEventListener){\n", "         el.addEventListener(type, handler, false);\n", "       } else if (el.attachEvent){\n", "         el.attachEvent(\"on\" + type, handler);\n", "       } else {\n", "         el[\"on\" + type] = handler;\n", "       }\n", "     }\n", "\n", "    var _origin_load = _vwo_code.load\n", "    _vwo_code.load = function () {\n", "      var args = [].slice.call(arguments)\n", "      bindEvent(window, 'load', function () {\n", "        _origin_load.apply(_vwo_code, args)\n", "      })\n", "    }\n", "  }()\n", "\n", "  _vwo_settings_timer = _vwo_code.init();\n", "  </script>\n", "\n", "\n", "    \n", "\n", "\n", "<script type=\"application/ld+json\">\n", "{\n", "  \"@context\": \"http://schema.org\",\n", "  \"name\": \"坚如磐石\",\n", "  \"url\": \"/subject/********/\",\n", "  \"image\": \"https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2896319483.jpg\",\n", "  \"director\": \n", "  [\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1054398/\",\n", "      \"name\": \"张艺谋 <PERSON><PERSON><PERSON>\"\n", "    }\n", "  ]\n", ",\n", "  \"author\": \n", "  [\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1317245/\",\n", "      \"name\": \"陈宇 Yu <PERSON>\"\n", "    }\n", "  ]\n", ",\n", "  \"actor\": \n", "  [\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1312940/\",\n", "      \"name\": \"雷佳音 <PERSON><PERSON><PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1015115/\",\n", "      \"name\": \"张国立 Guo<PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1313742/\",\n", "      \"name\": \"于和伟 <PERSON>wei <PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1274224/\",\n", "      \"name\": \"周冬雨 <PERSON><PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1313842/\",\n", "      \"name\": \"孙艺洲 Yizhou Sun\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1274530/\",\n", "      \"name\": \"李乃文 <PERSON><PERSON><PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1314762/\",\n", "      \"name\": \"许亚军 <PERSON><PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1316331/\",\n", "      \"name\": \"田雨 Yu T<PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1313462/\",\n", "      \"name\": \"何政军 <PERSON>jun <PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1358119/\",\n", "      \"name\": \"徐子力 Zili <PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1458274/\",\n", "      \"name\": \"林博洋 <PERSON><PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1458276/\",\n", "      \"name\": \"陈童 Tong <PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1045565/\",\n", "      \"name\": \"陈道明 <PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1044964/\",\n", "      \"name\": \"陈冲 <PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1317139/\",\n", "      \"name\": \"王迅 Xun <PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1314024/\",\n", "      \"name\": \"赵亮 <PERSON>\"\n", "    }\n", "    ,\n", "    {\n", "      \"@type\": \"Person\",\n", "      \"url\": \"/celebrity/1313991/\",\n", "      \"name\": \"焦刚 Gang Jiao\"\n", "    }\n", "  ]\n", ",\n", "  \"datePublished\": \"2023-09-28\",\n", "  \"genre\": [\"\\u5267\\u60c5\", \"\\u52a8\\u4f5c\", \"\\u72af\\u7f6a\"],\n", "  \"duration\": \"PT2H7M\",\n", "  \"description\": \"金江市副市长郑刚（张国立 饰）之子苏见明（雷佳音 饰）不顾父亲的劝阻，应邀赴约首富黎志田（于和伟 饰）的“鸿门宴”，不料却被迫观看了一出“人手下火锅”的猖狂戏码。旧案翻起，风雨欲来，各方蛰伏势力蠢蠢欲...\",\n", "  \"@type\": \"Movie\",\n", "  \"aggregateRating\": {\n", "    \"@type\": \"AggregateRating\",\n", "    \"ratingCount\": \"165106\",\n", "    \"bestRating\": \"10\",\n", "    \"worstRating\": \"2\",\n", "    \"ratingValue\": \"6.3\"\n", "  }\n", "}\n", "</script>\n", "\n", "    \n", "    \n", "    <meta property=\"og:title\" content=\"坚如磐石\" />\n", "    <meta property=\"og:description\" content=\"金江市副市长郑刚（张国立 饰）之子苏见明（雷佳音 饰）不顾父亲的劝阻，应邀赴约首富黎志田（于和伟 饰）的“鸿门宴”，不料却被迫观看了一出“人手下火锅”的猖狂戏码。旧案翻起，风雨欲来，各方蛰伏势力蠢蠢欲...\" />\n", "    <meta property=\"og:site_name\" content=\"豆瓣\" />\n", "    <meta property=\"og:url\" content=\"https://movie.douban.com/subject/********/\" />\n", "    <meta property=\"og:image\" content=\"https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2896319483.jpg\" />\n", "    <meta property=\"og:type\" content=\"video.movie\" />\n", "            <meta property=\"video:actor\" content=\"雷佳音\" />\n", "            <meta property=\"video:actor\" content=\"张国立\" />\n", "            <meta property=\"video:actor\" content=\"于和伟\" />\n", "            <meta property=\"video:actor\" content=\"周冬雨\" />\n", "            <meta property=\"video:actor\" content=\"孙艺洲\" />\n", "            <meta property=\"video:actor\" content=\"李乃文\" />\n", "            <meta property=\"video:actor\" content=\"许亚军\" />\n", "            <meta property=\"video:actor\" content=\"田雨\" />\n", "            <meta property=\"video:actor\" content=\"何政军\" />\n", "            <meta property=\"video:actor\" content=\"徐子力\" />\n", "            <meta property=\"video:actor\" content=\"林博洋\" />\n", "            <meta property=\"video:actor\" content=\"陈童\" />\n", "            <meta property=\"video:actor\" content=\"陈道明\" />\n", "            <meta property=\"video:actor\" content=\"陈冲\" />\n", "            <meta property=\"video:actor\" content=\"王迅\" />\n", "            <meta property=\"video:actor\" content=\"赵亮\" />\n", "            <meta property=\"video:actor\" content=\"焦刚\" />\n", "            <meta property=\"video:director\" content=\"张艺谋\" />\n", "        <meta property=\"video:duration\" content=\"7620\" />\n", "\n", "\n", "    <style type=\"text/css\">\n", "  \n", "</style>\n", "    <style type=\"text/css\">img { max-width: 100%; }</style>\n", "    <script type=\"text/javascript\"></script>\n", "    <link rel=\"stylesheet\" href=\"https://img1.doubanio.com/misc/mixed_static/3dc34bd89ae810d.css\">\n", "\n", "    <link rel=\"shortcut icon\" href=\"https://img1.doubanio.com/favicon.ico\" type=\"image/x-icon\">\n", "</head>\n", "\n", "<body>\n", "  \n", "    <script type=\"text/javascript\">var _body_start = new Date();</script>\n", "\n", "    \n", "    \n", "\n", "\n", "\n", "    <link href=\"//img3.doubanio.com/dae/accounts/resources/164cfd8/shire/bundle.css\" rel=\"stylesheet\" type=\"text/css\">\n", "\n", "\n", "\n", "<div id=\"db-global-nav\" class=\"global-nav\">\n", "  <div class=\"bd\">\n", "    \n", "<div class=\"top-nav-info\">\n", "  <a href=\"https://accounts.douban.com/passport/login?source=movie\" class=\"nav-login\" rel=\"nofollow\">登录/注册</a>\n", "</div>\n", "\n", "\n", "    <div class=\"top-nav-doubanapp\">\n", "  <a href=\"https://www.douban.com/doubanapp/app?channel=top-nav\" class=\"lnk-doubanapp\">下载豆瓣客户端</a>\n", "  <div id=\"doubanapp-tip\">\n", "    <a href=\"https://www.douban.com/doubanapp/app?channel=qipao\" class=\"tip-link\">豆瓣 <span class=\"version\">6.0</span> 全新发布</a>\n", "    <a href=\"javascript: void 0;\" class=\"tip-close\">×</a>\n", "  </div>\n", "  <div id=\"top-nav-appintro\" class=\"more-items\">\n", "    <p class=\"appintro-title\">豆瓣</p>\n", "    <p class=\"qrcode\">扫码直接下载</p>\n", "    <div class=\"download\">\n", "      <a href=\"https://www.douban.com/doubanapp/redirect?channel=top-nav&direct_dl=1&download=iOS\">iPhone</a>\n", "      <span>·</span>\n", "      <a href=\"https://www.douban.com/doubanapp/redirect?channel=top-nav&direct_dl=1&download=Android\" class=\"download-android\">Android</a>\n", "    </div>\n", "  </div>\n", "</div>\n", "\n", "    \n", "\n", "\n", "<div class=\"global-nav-items\">\n", "  <ul>\n", "    <li class=\"\">\n", "      <a href=\"https://www.douban.com\" target=\"_blank\" data-moreurl-dict=\"{&quot;from&quot;:&quot;top-nav-click-main&quot;,&quot;uid&quot;:&quot;0&quot;}\">豆瓣</a>\n", "    </li>\n", "    <li class=\"\">\n", "      <a href=\"https://book.douban.com\" target=\"_blank\" data-moreurl-dict=\"{&quot;from&quot;:&quot;top-nav-click-book&quot;,&quot;uid&quot;:&quot;0&quot;}\">读书</a>\n", "    </li>\n", "    <li class=\"on\">\n", "      <a href=\"https://movie.douban.com\"  data-moreurl-dict=\"{&quot;from&quot;:&quot;top-nav-click-movie&quot;,&quot;uid&quot;:&quot;0&quot;}\">电影</a>\n", "    </li>\n", "    <li class=\"\">\n", "      <a href=\"https://music.douban.com\" target=\"_blank\" data-moreurl-dict=\"{&quot;from&quot;:&quot;top-nav-click-music&quot;,&quot;uid&quot;:&quot;0&quot;}\">音乐</a>\n", "    </li>\n", "    <li class=\"\">\n", "      <a href=\"https://www.douban.com/location\" target=\"_blank\" data-moreurl-dict=\"{&quot;from&quot;:&quot;top-nav-click-location&quot;,&quot;uid&quot;:&quot;0&quot;}\">同城</a>\n", "    </li>\n", "    <li class=\"\">\n", "      <a href=\"https://www.douban.com/group\" target=\"_blank\" data-moreurl-dict=\"{&quot;from&quot;:&quot;top-nav-click-group&quot;,&quot;uid&quot;:&quot;0&quot;}\">小组</a>\n", "    </li>\n", "    <li class=\"\">\n", "      <a href=\"https://read.douban.com&#47;?dcs=top-nav&amp;dcm=douban\" target=\"_blank\" data-moreurl-dict=\"{&quot;from&quot;:&quot;top-nav-click-read&quot;,&quot;uid&quot;:&quot;0&quot;}\">阅读</a>\n", "    </li>\n", "    <li class=\"\">\n", "      <a href=\"https://fm.douban.com&#47;?from_=shire_top_nav\" target=\"_blank\" data-moreurl-dict=\"{&quot;from&quot;:&quot;top-nav-click-fm&quot;,&quot;uid&quot;:&quot;0&quot;}\">FM</a>\n", "    </li>\n", "    <li class=\"\">\n", "      <a href=\"https://time.douban.com&#47;?dt_time_source=douban-web_top_nav\" target=\"_blank\" data-moreurl-dict=\"{&quot;from&quot;:&quot;top-nav-click-time&quot;,&quot;uid&quot;:&quot;0&quot;}\">时间</a>\n", "    </li>\n", "    <li class=\"\">\n", "      <a href=\"https://market.douban.com&#47;?utm_campaign=douban_top_nav&amp;utm_source=douban&amp;utm_medium=pc_web\" target=\"_blank\" data-moreurl-dict=\"{&quot;from&quot;:&quot;top-nav-click-market&quot;,&quot;uid&quot;:&quot;0&quot;}\">豆品</a>\n", "    </li>\n", "  </ul>\n", "</div>\n", "\n", "  </div>\n", "</div>\n", "<script>\n", "  ;window._GLOBAL_NAV = {\n", "    DOUBAN_URL: \"https://www.douban.com\",\n", "    N_NEW_NOTIS: 0,\n", "    N_NEW_DOUMAIL: 0\n", "  };\n", "</script>\n", "\n", "\n", "\n", "    <script src=\"//img3.doubanio.com/dae/accounts/resources/164cfd8/shire/bundle.js\" defer=\"defer\"></script>\n", "\n", "\n", "\n", "\n", "    \n", "\n", "\n", "\n", "    <link href=\"//img3.doubanio.com/dae/accounts/resources/164cfd8/movie/bundle.css\" rel=\"stylesheet\" type=\"text/css\">\n", "\n", "\n", "\n", "\n", "<div id=\"db-nav-movie\" class=\"nav\">\n", "  <div class=\"nav-wrap\">\n", "  <div class=\"nav-primary\">\n", "    <div class=\"nav-logo\">\n", "      <a href=\"https:&#47;&#47;movie.douban.com\">豆瓣电影</a>\n", "    </div>\n", "    <div class=\"nav-search\">\n", "      <form action=\"https:&#47;&#47;search.douban.com&#47;movie/subject_search\" method=\"get\">\n", "        <fieldset>\n", "          <legend>搜索：</legend>\n", "          <label for=\"inp-query\">\n", "          </label>\n", "          <div class=\"inp\"><input id=\"inp-query\" name=\"search_text\" size=\"22\" maxlength=\"60\" placeholder=\"搜索电影、电视剧、综艺、影人\" value=\"\"></div>\n", "          <div class=\"inp-btn\"><input type=\"submit\" value=\"搜索\"></div>\n", "          <input type=\"hidden\" name=\"cat\" value=\"1002\" />\n", "        </fieldset>\n", "      </form>\n", "    </div>\n", "  </div>\n", "  </div>\n", "  <div class=\"nav-secondary\">\n", "    \n", "\n", "<div class=\"nav-items\">\n", "  <ul>\n", "    <li    ><a href=\"https://movie.douban.com/cinema/nowplaying/\"\n", "     >影讯&购票</a>\n", "    </li>\n", "    <li    ><a href=\"https://movie.douban.com/explore\"\n", "     >选电影</a>\n", "    </li>\n", "    <li    ><a href=\"https://movie.douban.com/tv/\"\n", "     >电视剧</a>\n", "    </li>\n", "    <li    ><a href=\"https://movie.douban.com/chart\"\n", "     >排行榜</a>\n", "    </li>\n", "    <li    ><a href=\"https://movie.douban.com/review/best/\"\n", "     >影评</a>\n", "    </li>\n", "    <li    ><a href=\"https://movie.douban.com/annual/2022?fullscreen=1&source=navigation\"\n", "     >2022年度榜单</a>\n", "    </li>\n", "    <li    ><a href=\"https://standbyme2022.douban.com/?autorotate=false&fullscreen=true&hidenav=true&source=web_navigation\"\n", "            target=\"_blank\"\n", "     >2022书影音报告</a>\n", "    </li>\n", "  </ul>\n", "</div>\n", "\n", "    <a href=\"https://movie.douban.com/annual/2022?fullscreen=1&source=movie_navigation\" class=\"movieannual\"></a>\n", "  </div>\n", "</div>\n", "\n", "<script id=\"suggResult\" type=\"text/x-jquery-tmpl\">\n", "  <li data-link=\"{{= url}}\">\n", "            <a href=\"{{= url}}\" onclick=\"moreurl(this, {from:'movie_search_sugg', query:'{{= keyword }}', subject_id:'{{= id}}', i: '{{= index}}', type: '{{= type}}'})\">\n", "            <img src=\"{{= img}}\" width=\"40\" />\n", "            <p>\n", "                <em>{{= title}}</em>\n", "                {{if year}}\n", "                    <span>{{= year}}</span>\n", "                {{/if}}\n", "                {{if sub_title}}\n", "                    <br /><span>{{= sub_title}}</span>\n", "                {{/if}}\n", "                {{if address}}\n", "                    <br /><span>{{= address}}</span>\n", "                {{/if}}\n", "                {{if episode}}\n", "                    {{if episode==\"unknow\"}}\n", "                        <br /><span>集数未知</span>\n", "                    {{else}}\n", "                        <br /><span>共{{= episode}}集</span>\n", "                    {{/if}}\n", "                {{/if}}\n", "            </p>\n", "        </a>\n", "        </li>\n", "  </script>\n", "\n", "\n", "\n", "\n", "    <script src=\"//img3.doubanio.com/dae/accounts/resources/164cfd8/movie/bundle.js\" defer=\"defer\"></script>\n", "\n", "\n", "\n", "\n", "\n", "    \n", "    <div id=\"wrapper\">\n", "        \n", "\n", "        \n", "    <div id=\"content\">\n", "        \n", "\n", "    <h1>\n", "        <span property=\"v:itemreviewed\">坚如磐石</span>\n", "            <span class=\"year\">(2023)</span>\n", "    </h1>\n", "\n", "        <div class=\"grid-16-8 clearfix\">\n", "            \n", "\n", "            \n", "            <div class=\"article\">\n", "                \n", "    \n", "\n", "\n", "\n", "        <div class=\"indent clearfix\">\n", "            <div class=\"subjectwrap clearfix\">\n", "                <div class=\"subject clearfix\">\n", "                    \n", "                    \n", "\n", "\n", "\n", "<div id=\"mainpic\" class=\"\">\n", "    <a class=\"nbgnbg\" href=\"https://movie.douban.com/subject/********/photos?type=R\" title=\"点击看更多海报\">\n", "        <img src=\"https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2896319483.jpg\" title=\"点击看更多海报\" alt=\"坚如磐石\" rel=\"v:image\" />\n", "   </a>\n", "</div>\n", "\n", "                    \n", "\n", "\n", "<div id=\"info\">\n", "        <span ><span class='pl'>导演</span>: <span class='attrs'><a href=\"/celebrity/1054398/\" rel=\"v:directedBy\">张艺谋</a></span></span><br/>\n", "        <span ><span class='pl'>编剧</span>: <span class='attrs'><a href=\"/celebrity/1317245/\">陈宇</a></span></span><br/>\n", "        <span class=\"actor\"><span class='pl'>主演</span>: <span class='attrs'><a href=\"/celebrity/1312940/\" rel=\"v:starring\">雷佳音</a> / <a href=\"/celebrity/1015115/\" rel=\"v:starring\">张国立</a> / <a href=\"/celebrity/1313742/\" rel=\"v:starring\">于和伟</a> / <a href=\"/celebrity/1274224/\" rel=\"v:starring\">周冬雨</a> / <a href=\"/celebrity/1313842/\" rel=\"v:starring\">孙艺洲</a> / <a href=\"/celebrity/1274530/\" rel=\"v:starring\">李乃文</a> / <a href=\"/celebrity/1314762/\" rel=\"v:starring\">许亚军</a> / <a href=\"/celebrity/1316331/\" rel=\"v:starring\">田雨</a> / <a href=\"/celebrity/1313462/\" rel=\"v:starring\">何政军</a> / <a href=\"/celebrity/1358119/\" rel=\"v:starring\">徐子力</a> / <a href=\"/celebrity/1458274/\" rel=\"v:starring\">林博洋</a> / <a href=\"/celebrity/1458276/\" rel=\"v:starring\">陈童</a> / <a href=\"/celebrity/1045565/\" rel=\"v:starring\">陈道明</a> / <a href=\"/celebrity/1044964/\" rel=\"v:starring\">陈冲</a> / <a href=\"/celebrity/1317139/\" rel=\"v:starring\">王迅</a> / <a href=\"/celebrity/1314024/\" rel=\"v:starring\">赵亮</a> / <a href=\"/celebrity/1313991/\" rel=\"v:starring\">焦刚</a></span></span><br/>\n", "        <span class=\"pl\">类型:</span> <span property=\"v:genre\">剧情</span> / <span property=\"v:genre\">动作</span> / <span property=\"v:genre\">犯罪</span><br/>\n", "        \n", "        <span class=\"pl\">制片国家/地区:</span> 中国大陆<br/>\n", "        <span class=\"pl\">语言:</span> 汉语普通话<br/>\n", "        <span class=\"pl\">上映日期:</span> <span property=\"v:initialReleaseDate\" content=\"2023-09-28(中国大陆)\">2023-09-28(中国大陆)</span><br/>\n", "        <span class=\"pl\">片长:</span> <span property=\"v:runtime\" content=\"127\">127分钟</span><br/>\n", "        <span class=\"pl\">又名:</span> 黑洞 / Under the Light<br/>\n", "        <span class=\"pl\">IMDb:</span> tt11448070<br>\n", "\n", "</div>\n", "\n", "\n", "\n", "\n", "                </div>\n", "                \n", "                    \n", "\n", "<link rel=\"stylesheet\" href=\"https://img1.doubanio.com/f/movie/b9636a640b0525c5be0809d093247e12d39aebbe/dist/movie/download-output-image/index.css\">\n", "<div id=\"interest_sectl\">\n", "    <div class=\"rating_wrap clearbox\" rel=\"v:rating\">\n", "        <div class=\"clearfix\">\n", "            <div class=\"rating_logo ll\">\n", "                豆瓣评分\n", "            </div>\n", "          <div class=\"output-btn-wrap rr\" style=\"display:none\">\n", "            <img src=\"https://img1.doubanio.com/f/movie/692e86756648f29457847c5cc5e161d6f6b8aaac/pics/movie/reference.png\" />\n", "            <a class=\"download-output-image\" href=\"#\">引用</a>\n", "          </div>\n", "        </div>\n", "        \n", "\n", "\n", "\n", "<div class=\"rating_self clearfix\" typeof=\"v:Rating\">\n", "    <strong class=\"ll rating_num\" property=\"v:average\">6.3</strong>\n", "    <span property=\"v:best\" content=\"10.0\"></span>\n", "    <div class=\"rating_right \">\n", "        <div class=\"ll bigstar bigstar30\"></div>\n", "        <div class=\"rating_sum\">\n", "                <a href=\"comments\" class=\"rating_people\">\n", "                    <span property=\"v:votes\">165165</span>人评价\n", "                </a>\n", "        </div>\n", "    </div>\n", "</div>\n", "<div class=\"ratings-on-weight\">\n", "    \n", "        <div class=\"item\">\n", "        \n", "        <span class=\"stars5 starstop\" title=\"力荐\">\n", "            5星\n", "        </span>\n", "        <div class=\"power\" style=\"width:11px\"></div>\n", "        <span class=\"rating_per\">7.8%</span>\n", "        <br />\n", "        </div>\n", "        <div class=\"item\">\n", "        \n", "        <span class=\"stars4 starstop\" title=\"推荐\">\n", "            4星\n", "        </span>\n", "        <div class=\"power\" style=\"width:38px\"></div>\n", "        <span class=\"rating_per\">26.9%</span>\n", "        <br />\n", "        </div>\n", "        <div class=\"item\">\n", "        \n", "        <span class=\"stars3 starstop\" title=\"还行\">\n", "            3星\n", "        </span>\n", "        <div class=\"power\" style=\"width:64px\"></div>\n", "        <span class=\"rating_per\">44.8%</span>\n", "        <br />\n", "        </div>\n", "        <div class=\"item\">\n", "        \n", "        <span class=\"stars2 starstop\" title=\"较差\">\n", "            2星\n", "        </span>\n", "        <div class=\"power\" style=\"width:22px\"></div>\n", "        <span class=\"rating_per\">15.6%</span>\n", "        <br />\n", "        </div>\n", "        <div class=\"item\">\n", "        \n", "        <span class=\"stars1 starstop\" title=\"很差\">\n", "            1星\n", "        </span>\n", "        <div class=\"power\" style=\"width:7px\"></div>\n", "        <span class=\"rating_per\">5.0%</span>\n", "        <br />\n", "        </div>\n", "</div>\n", "\n", "    </div>\n", "        <div class=\"rating_betterthan\">\n", "            好于 <a href=\"/typerank?type_name=犯罪&type=3&interval_id=75:65&action=\">71% 犯罪片</a><br/>\n", "            好于 <a href=\"/typerank?type_name=剧情&type=11&interval_id=70:60&action=\">66% 剧情片</a><br/>\n", "        </div>\n", "</div>\n", "<script src=\"https://img1.doubanio.com/f/movie/d9835c2c116dd65ad5f00817337abbb5445486ca/dist/movie/download-output-image/index.js\"></script>\n", "\n", "\n", "                \n", "            </div>\n", "            \n", "                \n", "\n", "\n", "\n", "\n", "\n", "<div id=\"interest_sect_level\" class=\"clearfix\">\n", "        \n", "            <a href=\"https://www.douban.com/reason=collectwish&amp;ck=\" rel=\"nofollow\" class=\"j a_show_login colbutt ll\" name=\"pbtn-********-wish\">\n", "                <span>想看</span>\n", "            </a>\n", "            <a href=\"https://www.douban.com/reason=collectcollect&amp;ck=\" rel=\"nofollow\" class=\"j a_show_login colbutt ll\" name=\"pbtn-********-collect\">\n", "                <span>看过</span>\n", "            </a>\n", "        <div class=\"ll j a_stars\">\n", "            \n", "    \n", "    评价:\n", "    <span id=\"rating\"> <span id=\"stars\" data-solid=\"https://img1.doubanio.com/f/shire/5a2327c04c0c231bced131ddf3f4467eb80c1c86/pics/rating_icons/star_onmouseover.png\" data-hollow=\"https://img1.doubanio.com/f/shire/2520c01967207a1735171056ec588c8c1257e5f8/pics/rating_icons/star_hollow_hover.png\" data-solid-2x=\"https://img1.doubanio.com/f/shire/7258904022439076d57303c3b06ad195bf1dc41a/pics/rating_icons/<EMAIL>\" data-hollow-2x=\"https://img1.doubanio.com/f/shire/95cc2fa733221bb8edd28ad56a7145a5ad33383e/pics/rating_icons/<EMAIL>\">\n", "\n", "            <a href=\"https://www.douban.com/register?reason=rate\" class=\"j a_show_login\" name=\"pbtn-********-1\">\n", "            <img src=\"https://img1.doubanio.com/f/shire/2520c01967207a1735171056ec588c8c1257e5f8/pics/rating_icons/star_hollow_hover.png\" id=\"star1\" width=\"16\" height=\"16\"/>\n", "        </a>\n", "            <a href=\"https://www.douban.com/register?reason=rate\" class=\"j a_show_login\" name=\"pbtn-********-2\">\n", "            <img src=\"https://img1.doubanio.com/f/shire/2520c01967207a1735171056ec588c8c1257e5f8/pics/rating_icons/star_hollow_hover.png\" id=\"star2\" width=\"16\" height=\"16\"/>\n", "        </a>\n", "            <a href=\"https://www.douban.com/register?reason=rate\" class=\"j a_show_login\" name=\"pbtn-********-3\">\n", "            <img src=\"https://img1.doubanio.com/f/shire/2520c01967207a1735171056ec588c8c1257e5f8/pics/rating_icons/star_hollow_hover.png\" id=\"star3\" width=\"16\" height=\"16\"/>\n", "        </a>\n", "            <a href=\"https://www.douban.com/register?reason=rate\" class=\"j a_show_login\" name=\"pbtn-********-4\">\n", "            <img src=\"https://img1.doubanio.com/f/shire/2520c01967207a1735171056ec588c8c1257e5f8/pics/rating_icons/star_hollow_hover.png\" id=\"star4\" width=\"16\" height=\"16\"/>\n", "        </a>\n", "            <a href=\"https://www.douban.com/register?reason=rate\" class=\"j a_show_login\" name=\"pbtn-********-5\">\n", "            <img src=\"https://img1.doubanio.com/f/shire/2520c01967207a1735171056ec588c8c1257e5f8/pics/rating_icons/star_hollow_hover.png\" id=\"star5\" width=\"16\" height=\"16\"/>\n", "        </a>\n", "    </span><span id=\"rateword\" class=\"pl\"></span>\n", "    <input id=\"n_rating\" type=\"hidden\" value=\"\"  />\n", "    </span>\n", "\n", "        </div>\n", "    \n", "\n", "</div>\n", "\n", "\n", "            \n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "<div class=\"gtleft\">\n", "    <ul class=\"ul_subject_menu bicelink color_gray pt6 clearfix\">\n", "        \n", "    \n", "        \n", "                \n", "                  <li> \n", "    <img src=\"https://img1.doubanio.com/f/movie/cc03d0fcf32b7ce3af7b160a0b85e5e66b47cc42/pics/movie/short-comment.gif\" />&nbsp;\n", "        <a onclick=\"moreurl(this, {from:'mv_sbj_wr_cmnt_login'})\" class=\"j a_show_login\" href=\"https://www.douban.com/register?reason=review\" rel=\"nofollow\">写短评</a>\n", " </li>\n", "                  <li> \n", "    \n", "    <img src=\"https://img1.doubanio.com/f/movie/5bbf02b7b5ec12b23e214a580b6f9e481108488c/pics/movie/add-review.gif\" />&nbsp;\n", "        <a onclick=\"moreurl(this, {from:'mv_sbj_wr_rv_login'})\" class=\"j a_show_login\" href=\"https://www.douban.com/register?reason=review\" rel=\"nofollow\">写影评</a>\n", " </li>\n", "                    <li> \n", "   \n", "\n", "   \n", "    \n", "    <span class=\"rec\" id=\"电影-********\">\n", "    <a href= \"#\"\n", "        data-type=\"电影\"\n", "        data-url=\"https://movie.douban.com/subject/********/\"\n", "        data-desc=\"电影《坚如磐石》 (来自豆瓣) \"\n", "        data-title=\"电影《坚如磐石》 (来自豆瓣) \"\n", "        data-pic=\"https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2896319483.jpeg\"\n", "        class=\"bn-sharing \">\n", "        分享到\n", "    </a> &nbsp;&nbsp;\n", "    </span>\n", "    <link rel=\"stylesheet\" href=\"https://img1.doubanio.com/f/vendors/0035bb2f83e2cba49ecf634fed57f9ff1bbd0d09/css/ui/dialog.css\">\n", "    <script src=\"https://img1.doubanio.com/f/vendors/f25ae221544f39046484a823776f3aa01769ee10/js/ui/dialog.js\"></script>\n", "    <script src=\"https://img1.doubanio.com/f/vendors/b6e0770163b1da14217b0f1ca39189d47b95f51f/js/lib/sharebutton.js\"></script>\n", "    <script src=\"https://img1.doubanio.com/f/movie/c4ab132ff4d3d64a83854c875ea79b8b541faf12/dist/movie/libs/qrcode.min.js\"></script>\n", "\n", "  </li>\n", "            \n", "\n", "    </ul>\n", "\n", "    <script type=\"text/javascript\">\n", "        $(function(){\n", "            $(\".ul_subject_menu li.rec .bn-sharing\").bind(\"click\", function(){\n", "                $.get(\"/blank?sbj_page_click=bn_sharing\");\n", "            });\n", "        });\n", "    </script>\n", "</div>\n", "\n", "\n", "\n", "\n", "            \n", "                \n", "\n", "\n", "\n", "\n", "\n", "<div class=\"rec-sec\">\n", "<span class=\"rec\">\n", "    <script id=\"movie-share\" type=\"text/x-html-snippet\">\n", "        \n", "    <form class=\"movie-share\" action=\"/j/share\" method=\"POST\">\n", "        <div class=\"clearfix form-bd\">\n", "            <div class=\"input-area\">\n", "                <textarea name=\"text\" class=\"share-text\" cols=\"72\" data-mention-api=\"https://api.douban.com/shuo/in/complete?alt=xd&amp;callback=?\"></textarea>\n", "                <input type=\"hidden\" name=\"target-id\" value=\"********\">\n", "                <input type=\"hidden\" name=\"target-type\" value=\"0\">\n", "                <input type=\"hidden\" name=\"title\" value=\"坚如磐石‎ (2023)\">\n", "                <input type=\"hidden\" name=\"desc\" value=\"导演 张艺谋 主演 雷佳音 / 张国立 / 中国大陆 / 6.3分(165165评价)\">\n", "                <input type=\"hidden\" name=\"redir\" value=\"\"/>\n", "                <div class=\"mentioned-highlighter\"></div>\n", "            </div>\n", "\n", "            <div class=\"info-area\">\n", "                    <img class=\"media\" src=\"https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2896319483.jpg\" />\n", "                <strong>坚如磐石‎ (2023)</strong>\n", "                <p>导演 张艺谋 主演 雷佳音 / 张国立 / 中国大陆 / 6.3分(165165评价)</p>\n", "                <p class=\"error server-error\">&nbsp;</p>\n", "            </div>\n", "        </div>\n", "        <div class=\"form-ft\">\n", "            <div class=\"form-ft-inner\">\n", "                \n", "\n", "\n", "\n", "                <span class=\"avail-num-indicator\">140</span>\n", "                <span class=\"bn-flat\">\n", "                    <input type=\"submit\" value=\"推荐\" />\n", "                </span>\n", "            </div>\n", "        </div>\n", "    </form>\n", "    \n", "    <div id=\"suggest-mention-tmpl\" style=\"display:none;\">\n", "        <ul>\n", "            {{#users}}\n", "            <li id=\"{{uid}}\">\n", "              <img src=\"{{avatar}}\">{{{username}}}&nbsp;<span>({{{uid}}})</span>\n", "            </li>\n", "            {{/users}}\n", "        </ul>\n", "    </div>\n", "\n", "\n", "    </script>\n", "\n", "        \n", "        <a href=\"/accounts/register?reason=recommend\"  class=\"j a_show_login lnk-sharing\" \n", "            share-id=\"********\" \n", "            data-mode=\"plain\" data-name=\"坚如磐石‎ (2023)\" \n", "            data-type=\"movie\" data-desc=\"导演 张艺谋 主演 雷佳音 / 张国立 / 中国大陆 / 6.3分(165165评价)\" \n", "            data-href=\"https://movie.douban.com/subject/********/\" data-image=\"https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2896319483.jpg\" \n", "            data-properties=\"{}\" \n", "            data-redir=\"\" data-text=\"\" \n", "            data-apikey=\"\" data-curl=\"\" \n", "            data-count=\"10\" data-object_kind=\"1002\" \n", "            data-object_id=\"********\" data-target_type=\"rec\" \n", "            data-target_action=\"0\" \n", "            data-action_props=\"{&#34;subject_url&#34;:&#34;https:\\/\\/movie.douban.com\\/subject\\/********\\/&#34;,&#34;subject_title&#34;:&#34;坚如磐石‎ (2023)&#34;}\">推荐</a>\n", "</span>\n", "\n", "\n", "</div>\n", "\n", "\n", "\n", "\n", "\n", "\n", "            <script type=\"text/javascript\">\n", "                $(function() {\n", "                    $('.collect_btn', '#interest_sect_level').each(function() {\n", "                        Douban.init_collect_btn(this);\n", "                    });\n", "                    $('html').delegate(\".indent .rec-sec .lnk-sharing\", \"click\", function() {\n", "                        moreurl(this, {\n", "                            from : 'mv_sbj_db_share'\n", "                        });\n", "                    });\n", "                });\n", "            </script>\n", "        </div>\n", "            \n", "\n", "\n", "    <div id=\"collect_form_********\"></div>\n", "\n", "\n", "        \n", "\n", "\n", "\n", "<div class=\"related-info\" style=\"margin-bottom:-10px;\">\n", "    <a name=\"intro\"></a>\n", "    \n", "        \n", "            \n", "            \n", "    <h2>\n", "        <i class=\"\">坚如磐石的剧情简介</i>\n", "              · · · · · ·\n", "    </h2>\n", "\n", "            <div class=\"indent\" id=\"link-report-intra\">\n", "                    \n", "                        <span property=\"v:summary\" class=\"\">\n", "                                　　金江市副市长郑刚（张国立 饰）之子苏见明（雷佳音 饰）不顾父亲的劝阻，应邀赴约首富黎志田（于和伟 饰）的“鸿门宴”，不料却被迫观看了一出“人手下火锅”的猖狂戏码。旧案翻起，风雨欲来，各方蛰伏势力蠢蠢欲动，筹谋与算计、审视与怀疑，光怪陆离之中，人性欲望翻腾，谁将撕去最后的面具？\n", "                        </span>\n", "                        <div class=\"clear\"></div>\n", "                        \n", "\n", "            </div>\n", "</div>\n", "\n", "\n", "    <div id=\"dale_movie_subject_banner_after_intro\"></div>\n", "\n", "    \n", "\n", "\n", "\n", "\n", "\n", "<link rel=\"stylesheet\" href=\"https://img1.doubanio.com/f/movie/e7bb3fa21985f6e07c027dd984c99f26e54987f9/dist/movie/celebrity/celebrities_section.css\">\n", "\n", "<div id=\"celebrities\" class=\"celebrities related-celebrities\">\n", "\n", "  \n", "    <h2>\n", "        <i class=\"\">坚如磐石的演职员</i>\n", "              · · · · · ·\n", "            <span class=\"pl\">\n", "            (\n", "                <a href=\"/subject/********/celebrities\">全部 27</a>\n", "            )\n", "            </span>\n", "    </h2>\n", "\n", "\n", "  <ul class=\"celebrities-list from-subject __oneline\">\n", "        \n", "    \n", "  \n", "  <li class=\"celebrity\">\n", "    \n", "\n", "  <a href=\"https://movie.douban.com/celebrity/1054398/\" title=\"张艺谋 <PERSON><PERSON><PERSON>\" class=\"\">\n", "      <div class=\"avatar\" style=\"background-image: url(https://img2.doubanio.com/view/personage/raw/public/c18c0962e339ca8b6f1d9e8be73cc8d1.jpg)\">\n", "    </div>\n", "  </a>\n", "\n", "    <div class=\"info\">\n", "      <span class=\"name\"><a href=\"https://movie.douban.com/celebrity/1054398/\" title=\"张艺谋 <PERSON><PERSON><PERSON>\" class=\"name\">张艺谋</a></span>\n", "\n", "      <span class=\"role\" title=\"导演\">导演</span>\n", "\n", "    </div>\n", "  </li>\n", "\n", "\n", "        \n", "    \n", "  \n", "  <li class=\"celebrity\">\n", "    \n", "\n", "  <a href=\"https://movie.douban.com/celebrity/1312940/\" title=\"雷佳音 <PERSON><PERSON><PERSON>\" class=\"has-account\">\n", "      <div class=\"avatar has-account\" style=\"background-image: url(https://img1.doubanio.com/f/movie/14960825e118267b5857fc0ae9f306ef8c74da8f/pics/movie/<EMAIL>), url(https://img2.doubanio.com/view/personage/raw/public/ab47047fcc27f3d968aa7ee484e3fcde.jpg)\">\n", "    </div>\n", "  </a>\n", "\n", "    <div class=\"info\">\n", "      <span class=\"name\"><a href=\"https://movie.douban.com/celebrity/1312940/\" title=\"雷佳音 <PERSON><PERSON><PERSON>\" class=\"name\">雷佳音</a></span>\n", "\n", "      <span class=\"role\" title=\"饰 苏见明\">饰 苏见明</span>\n", "\n", "    </div>\n", "  </li>\n", "\n", "\n", "        \n", "    \n", "  \n", "  <li class=\"celebrity\">\n", "    \n", "\n", "  <a href=\"https://movie.douban.com/celebrity/1015115/\" title=\"张国立 Guoli Zhang\" class=\"\">\n", "      <div class=\"avatar\" style=\"background-image: url(https://img1.doubanio.com/view/personage/raw/public/5683455ce0c6f99e7f7875db750660ac.jpg)\">\n", "    </div>\n", "  </a>\n", "\n", "    <div class=\"info\">\n", "      <span class=\"name\"><a href=\"https://movie.douban.com/celebrity/1015115/\" title=\"张国立 Guoli Zhang\" class=\"name\">张国立</a></span>\n", "\n", "      <span class=\"role\" title=\"饰 郑刚\">饰 郑刚</span>\n", "\n", "    </div>\n", "  </li>\n", "\n", "\n", "        \n", "    \n", "  \n", "  <li class=\"celebrity\">\n", "    \n", "\n", "  <a href=\"https://movie.douban.com/celebrity/1313742/\" title=\"于和伟 Hewei Yu\" class=\"\">\n", "      <div class=\"avatar\" style=\"background-image: url(https://img9.doubanio.com/view/personage/raw/public/1f6c05f6fab02bbd59490115e3372805.jpg)\">\n", "    </div>\n", "  </a>\n", "\n", "    <div class=\"info\">\n", "      <span class=\"name\"><a href=\"https://movie.douban.com/celebrity/1313742/\" title=\"于和伟 Hewei Yu\" class=\"name\">于和伟</a></span>\n", "\n", "      <span class=\"role\" title=\"饰 黎志田\">饰 黎志田</span>\n", "\n", "    </div>\n", "  </li>\n", "\n", "\n", "        \n", "    \n", "  \n", "  <li class=\"celebrity\">\n", "    \n", "\n", "  <a href=\"https://movie.douban.com/celebrity/1274224/\" title=\"周冬雨 Dongyu Zhou\" class=\"\">\n", "      <div class=\"avatar\" style=\"background-image: url(https://img9.doubanio.com/view/celebrity/raw/public/p1588997827.35.jpg)\">\n", "    </div>\n", "  </a>\n", "\n", "    <div class=\"info\">\n", "      <span class=\"name\"><a href=\"https://movie.douban.com/celebrity/1274224/\" title=\"周冬雨 Dongyu Zhou\" class=\"name\">周冬雨</a></span>\n", "\n", "      <span class=\"role\" title=\"饰 李惠琳\">饰 李惠琳</span>\n", "\n", "    </div>\n", "  </li>\n", "\n", "\n", "        \n", "    \n", "  \n", "  <li class=\"celebrity\">\n", "    \n", "\n", "  <a href=\"https://movie.douban.com/celebrity/1313842/\" title=\"孙艺洲 Yizhou Sun\" class=\"\">\n", "      <div class=\"avatar\" style=\"background-image: url(https://img1.doubanio.com/view/personage/raw/public/809aaaf65bc8f26caaca30e4dec9fc6a.jpg)\">\n", "    </div>\n", "  </a>\n", "\n", "    <div class=\"info\">\n", "      <span class=\"name\"><a href=\"https://movie.douban.com/celebrity/1313842/\" title=\"孙艺洲 Yizhou Sun\" class=\"name\">孙艺洲</a></span>\n", "\n", "      <span class=\"role\" title=\"饰 David\">饰 <PERSON></span>\n", "\n", "    </div>\n", "  </li>\n", "\n", "\n", "  </ul>\n", "</div>\n", "\n", "\n", "    \n", "\n", "\n", "<link rel=\"stylesheet\" href=\"https://img1.doubanio.com/f/verify/b94df3c30a99176a78709b95ecf1cf80b105beb0/entry_creator/dist/author_subject/style.css\">\n", "<div id=\"author_subject\" class=\"author-wrapper\">\n", "    <div class=\"loading\"></div>\n", "</div>\n", "<script type=\"text/javascript\">\n", "    var answerObj = {\n", "      ISALL: 'False',\n", "      TYPE: 'movie',\n", "      SUBJECT_ID: '********',\n", "      USER_ID: 'None'\n", "    }\n", "</script>\n", "<script type=\"text/javascript\" src=\"https://img1.doubanio.com/f/vendors/bd6325a12f40c34cbf2668aafafb4ccd60deab7e/vendors.js\"></script>\n", "<script type=\"text/javascript\" src=\"https://img1.doubanio.com/f/vendors/6242a400cfd25992da35ace060e58f160efc9c50/shared_rc.js\"></script>\n", "<script type=\"text/javascript\" src=\"https://img1.doubanio.com/f/verify/6e7e593ba318f1355c70e66e010cf89411c3d937/entry_creator/dist/author_subject/index.js\"></script>\n", "\n", "\n", "    \n", "        \n", "\n", "\n", "\n", "<link rel=\"stylesheet\" href=\"https://img1.doubanio.com/f/movie/0164f79cda68b2b3cb09b712e8417001e4a4ca1a/dist/movie/subject/photos_section.css\">\n", "\n", "\n", "\n", "\n", "\n", "    \n", "    <div id=\"related-pic\" class=\"related-pic\">\n", "        \n", "    \n", "    \n", "    <h2>\n", "        <i class=\"\">坚如磐石的视频和图片</i>\n", "              · · · · · ·\n", "            <span class=\"pl\">\n", "            (\n", "                <a href=\"https://movie.douban.com/subject/********/trailer#trailer\">预告片14</a>&nbsp;|&nbsp;<a href=\"/video/create?subject_id=********\">添加视频评论</a>&nbsp;|&nbsp;<a href=\"https://movie.douban.com/subject/********/all_photos\">图片267</a>&nbsp;·&nbsp;<a href=\"https://movie.douban.com/subject/********/mupload\">添加</a>\n", "            )\n", "            </span>\n", "    </h2>\n", "\n", "\n", "        <ul class=\"related-pic-bd  \">\n", "                <li class=\"label-trailer\">\n", "                    <a class=\"related-pic-video\" href=\"https://movie.douban.com/trailer/310321/#content\" title=\"预告片\" style=\"background-image:url(https://img2.doubanio.com/img/trailer/medium/2898522322.jpg)\">\n", "                    </a>\n", "                </li>\n", "                <li>\n", "                    <a href=\"https://movie.douban.com/photos/photo/2897715719/\"><img src=\"https://img1.doubanio.com/view/photo/sqxs/public/p2897715719.jpg\" alt=\"图片\" /></a>\n", "                </li>\n", "                <li>\n", "                    <a href=\"https://movie.douban.com/photos/photo/2897715720/\"><img src=\"https://img1.doubanio.com/view/photo/sqxs/public/p2897715720.jpg\" alt=\"图片\" /></a>\n", "                </li>\n", "                <li>\n", "                    <a href=\"https://movie.douban.com/photos/photo/2897715721/\"><img src=\"https://img2.doubanio.com/view/photo/sqxs/public/p2897715721.jpg\" alt=\"图片\" /></a>\n", "                </li>\n", "                <li>\n", "                    <a href=\"https://movie.douban.com/photos/photo/2897988152/\"><img src=\"https://img2.doubanio.com/view/photo/sqxs/public/p2897988152.jpg\" alt=\"图片\" /></a>\n", "                </li>\n", "        </ul>\n", "    </div>\n", "\n", "\n", "\n", "    \n", "\n", "    \n", "\n", "\n", "\n", "<style type=\"text/css\">\n", ".award li { display: inline; margin-right: 5px }\n", ".awards { margin-bottom: 20px }\n", ".awards h2 { background: none; color: #000; font-size: 14px; padding-bottom: 5px; margin-bottom: 8px; border-bottom: 1px dashed #dddddd }\n", ".awards .year { color: #666666; margin-left: -5px }\n", ".mod { margin-bottom: 25px }\n", ".mod .hd { margin-bottom: 10px }\n", ".mod .hd h2 {margin:24px 0 3px 0}\n", "</style>\n", "\n", "\n", "\n", "\n", "    \n", "        \n", "\n", "\n", "\n", "<link rel=\"stylesheet\" href=\"https://img1.doubanio.com/f/movie/0ebe45fa18ae69ea021b9ea03936d3656d4a5581/dist/movie/subject/recommendations.css\">\n", "\n", "\n", "\n", "\n", "    <div id=\"recommendations\" class=\"\">\n", "        \n", "        \n", "    <h2>\n", "        <i class=\"\">喜欢这部电影的人也喜欢</i>\n", "              · · · · · ·\n", "    </h2>\n", "\n", "        \n", "    \n", "    <div class=\"recommendations-bd\">\n", "        <dl class=\"\">\n", "            <dt>\n", "                <a href=\"https://movie.douban.com/subject/33456512/?from=subject-page\" >\n", "                    <img src=\"https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2629365857.jpg\" alt=\"涉过愤怒的海\" class=\"\" />\n", "                </a>\n", "            </dt>\n", "            <dd>\n", "                <a href=\"https://movie.douban.com/subject/33456512/?from=subject-page\" class=\"\" >涉过愤怒的海</a>\n", "                <span class=\"subject-rate\"></span>\n", "            </dd>\n", "        </dl>\n", "        <dl class=\"\">\n", "            <dt>\n", "                <a href=\"https://movie.douban.com/subject/35074609/?from=subject-page\" >\n", "                    <img src=\"https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2896951257.jpg\" alt=\"金手指\" class=\"\" />\n", "                </a>\n", "            </dt>\n", "            <dd>\n", "                <a href=\"https://movie.douban.com/subject/35074609/?from=subject-page\" class=\"\" >金手指</a>\n", "                <span class=\"subject-rate\"></span>\n", "            </dd>\n", "        </dl>\n", "        <dl class=\"\">\n", "            <dt>\n", "                <a href=\"https://movie.douban.com/subject/34436452/?from=subject-page\" >\n", "                    <img src=\"https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2897543922.jpg\" alt=\"第八个嫌疑人\" class=\"\" />\n", "                </a>\n", "            </dt>\n", "            <dd>\n", "                <a href=\"https://movie.douban.com/subject/34436452/?from=subject-page\" class=\"\" >第八个嫌疑人</a>\n", "                <span class=\"subject-rate\">6.2</span>\n", "            </dd>\n", "        </dl>\n", "        <dl class=\"\">\n", "            <dt>\n", "                <a href=\"https://movie.douban.com/subject/26351864/?from=subject-page\" >\n", "                    <img src=\"https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2500366160.jpg\" alt=\"风林火山\" class=\"\" />\n", "                </a>\n", "            </dt>\n", "            <dd>\n", "                <a href=\"https://movie.douban.com/subject/26351864/?from=subject-page\" class=\"\" >风林火山</a>\n", "                <span class=\"subject-rate\"></span>\n", "            </dd>\n", "        </dl>\n", "        <dl class=\"\">\n", "            <dt>\n", "                <a href=\"https://movie.douban.com/subject/26903951/?from=subject-page\" >\n", "                    <img src=\"https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2628080438.jpg\" alt=\"三叉戟\" class=\"\" />\n", "                </a>\n", "            </dt>\n", "            <dd>\n", "                <a href=\"https://movie.douban.com/subject/26903951/?from=subject-page\" class=\"\" >三叉戟</a>\n", "                <span class=\"subject-rate\"></span>\n", "            </dd>\n", "        </dl>\n", "        <dl class=\"\">\n", "            <dt>\n", "                <a href=\"https://movie.douban.com/subject/26745332/?from=subject-page\" >\n", "                    <img src=\"https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2897460998.jpg\" alt=\"花月杀手\" class=\"\" />\n", "                </a>\n", "            </dt>\n", "            <dd>\n", "                <a href=\"https://movie.douban.com/subject/26745332/?from=subject-page\" class=\"\" >花月杀手</a>\n", "                <span class=\"subject-rate\">8.1</span>\n", "            </dd>\n", "        </dl>\n", "        <dl class=\"\">\n", "            <dt>\n", "                <a href=\"https://movie.douban.com/subject/26938697/?from=subject-page\" >\n", "                    <img src=\"https://img9.doubanio.com/view/photo/s_ratio_poster/public/p2627038995.jpg\" alt=\"猎狐行动\" class=\"\" />\n", "                </a>\n", "            </dt>\n", "            <dd>\n", "                <a href=\"https://movie.douban.com/subject/26938697/?from=subject-page\" class=\"\" >猎狐行动</a>\n", "                <span class=\"subject-rate\"></span>\n", "            </dd>\n", "        </dl>\n", "        <dl class=\"\">\n", "            <dt>\n", "                <a href=\"https://movie.douban.com/subject/35927496/?from=subject-page\" >\n", "                    <img src=\"https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2898360661.jpg\" alt=\"潜行\" class=\"\" />\n", "                </a>\n", "            </dt>\n", "            <dd>\n", "                <a href=\"https://movie.douban.com/subject/35927496/?from=subject-page\" class=\"\" >潜行</a>\n", "                <span class=\"subject-rate\"></span>\n", "            </dd>\n", "        </dl>\n", "        <dl class=\"\">\n", "            <dt>\n", "                <a href=\"https://movie.douban.com/subject/27125428/?from=subject-page\" >\n", "                    <img src=\"https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2532642680.jpg\" alt=\"灭相\" class=\"\" />\n", "                </a>\n", "            </dt>\n", "            <dd>\n", "                <a href=\"https://movie.douban.com/subject/27125428/?from=subject-page\" class=\"\" >灭相</a>\n", "                <span class=\"subject-rate\"></span>\n", "            </dd>\n", "        </dl>\n", "        <dl class=\"\">\n", "            <dt>\n", "                <a href=\"https://movie.douban.com/subject/34879969/?from=subject-page\" >\n", "                    <img src=\"https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2790968563.jpg\" alt=\"平原上的火焰\" class=\"\" />\n", "                </a>\n", "            </dt>\n", "            <dd>\n", "                <a href=\"https://movie.douban.com/subject/34879969/?from=subject-page\" class=\"\" >平原上的火焰</a>\n", "                <span class=\"subject-rate\"></span>\n", "            </dd>\n", "        </dl>\n", "    </div>\n", "\n", "    </div>\n", "\n", "\n", "\n", "    \n", "        \n", "\n", "\n", "\n", "<script type=\"text/x-handlebar-tmpl\" id=\"comment-tmpl\">\n", "    <div class=\"dummy-fold\">\n", "        {{#each comments}}\n", "        <div class=\"comment-item\" data-cid=\"id\">\n", "            <div class=\"comment\">\n", "                <h3>\n", "                    <span class=\"comment-vote\">\n", "                            <span class=\"votes\">{{votes}}</span>\n", "                        <input value=\"{{id}}\" type=\"hidden\"/>\n", "                        <a href=\"javascript:;\" class=\"j {{#if ../if_logined}}vote-comment{{else}}a_show_login{{/if}}\">有用</a>\n", "                    </span>\n", "                    <span class=\"comment-info\">\n", "                        <a href=\"{{user.path}}\" class=\"\">{{user.name}}</a>\n", "                        {{#if rating}}\n", "                        <span class=\"allstar{{rating}}0 rating\" title=\"{{rating_word}}\"></span>\n", "                        {{/if}}\n", "                        <span>\n", "                            {{time}}\n", "                        </span>\n", "                        <p> {{content_tmpl content}} </p>\n", "                    </span>\n", "            </div>\n", "        </div>\n", "        {{/each}}\n", "    </div>\n", "</script>\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "    \n", "    <link rel=\"stylesheet\" href=\"https://img1.doubanio.com/f/movie/d63a579a99fd372b4398731a279a1382e6eac71e/dist/movie/subject-comments/comments-section.css\">\n", "\n", "    <div id=\"comments-section\">\n", "        <div class=\"mod-hd\">\n", "            \n", "            \n", "        <a class=\"comment_btn j a_show_login\" href=\"https://www.douban.com/register?reason=review\" rel=\"nofollow\">\n", "            <span>我要写短评</span>\n", "        </a>\n", "\n", "            \n", "    <h2>\n", "        <i class=\"\">坚如磐石的短评</i>\n", "              · · · · · ·\n", "            <span class=\"pl\">\n", "            (\n", "                <a href=\"https://movie.douban.com/subject/********/comments?status=P\">全部 77647 条</a>\n", "            )\n", "            </span>\n", "    </h2>\n", "\n", "        </div>\n", "       \n", "        \n", "\n", "\n", "        <div class=\"mod-bd\">\n", "                \n", "        <div class=\"tab-hd\">\n", "                        <a id=\"hot-comments-tab\" href=\"comments\" data-id=\"hot\" class=\"on\">热门</a>&nbsp;/&nbsp;\n", "                        <a id=\"new-comments-tab\" href=\"comments?sort=time\" data-id=\"new\">最新</a>&nbsp;/&nbsp;\n", "                        <a id=\"following-comments-tab\" href=\"comments?sort=follows\" data-id=\"following\"  class=\"j a_show_login\">好友</a>\n", "        </div>\n", "\n", "    <div class=\"tab-bd\">\n", "        <div id=\"hot-comments\" class=\"tab\">\n", "            \n", "    \n", "\n", "        \n", "        <div class=\"comment-item \" data-cid=\"3891564167\">\n", "            \n", "    \n", "    <div class=\"comment\">\n", "        <h3>\n", "            <span class=\"comment-vote\">\n", "                    <span class=\"votes vote-count\">6661</span>\n", "\n", "                    <input value=\"3891564167\" type=\"hidden\"/>\n", "                    <a href=\"javascript:;\" data-id=\"3891564167\"\n", "                        class=\"j a_show_login\" \n", "                        onclick=\"\">有用</a>\n", "                    \n", "                <!-- 删除短评 -->\n", "            </span>\n", "            <span class=\"comment-info\">\n", "                <a href=\"https://www.douban.com/people/81605499/\" class=\"\">鱼虾入梦ee</a>\n", "                    <span>看过</span>\n", "                    <span class=\"allstar30 rating\" title=\"还行\"></span>\n", "                <span class=\"comment-time \" title=\"2023-09-29 23:54:15\">\n", "                    2023-09-29 23:54:15\n", "                </span>\n", "                <span class=\"comment-location\">河南</span>\n", "            </span>\n", "        </h3>\n", "        <p class=\" comment-content\">\n", "            \n", "                <span class=\"short\">周冬雨说的那句这就叫爱情是哪个逼写的台词啊救命，比金五公司的宣传片还他妈土</span>\n", "        </p>\n", "        <div class=\"comment-report\" data-url=\"https://movie.douban.com/subject/********/?comment_id=3891564167\"></div>\n", "    </div>\n", "    <script>\n", "        (function(){\n", "            $(\"body\").delegate(\".comment-item\", 'mouseenter mouseleave', function (e) {\n", "                switch (e.type) {\n", "                    case \"mouseenter\":\n", "                    $(this).find(\".comment-report\").css('visibility', 'visible');\n", "                    break;\n", "                    case \"mouseleave\":\n", "                    $(this).find(\".comment-report\").css('visibility', 'hidden');\n", "                    break;\n", "                }\n", "            });\n", "        })()\n", "    </script>\n", "\n", "        </div>\n", "        \n", "        <div class=\"comment-item \" data-cid=\"3945429647\">\n", "            \n", "    \n", "    <div class=\"comment\">\n", "        <h3>\n", "            <span class=\"comment-vote\">\n", "                    <span class=\"votes vote-count\">4668</span>\n", "\n", "                    <input value=\"3945429647\" type=\"hidden\"/>\n", "                    <a href=\"javascript:;\" data-id=\"3945429647\"\n", "                        class=\"j a_show_login\" \n", "                        onclick=\"\">有用</a>\n", "                    \n", "                <!-- 删除短评 -->\n", "            </span>\n", "            <span class=\"comment-info\">\n", "                <a href=\"https://www.douban.com/people/diewithme/\" class=\"\">王大根</a>\n", "                    <span>看过</span>\n", "                    <span class=\"allstar30 rating\" title=\"还行\"></span>\n", "                <span class=\"comment-time \" title=\"2023-09-28 20:39:22\">\n", "                    2023-09-28 20:39:22\n", "                </span>\n", "                <span class=\"comment-location\">北京</span>\n", "            </span>\n", "        </h3>\n", "        <p class=\" comment-content\">\n", "            \n", "                <span class=\"short\">看完这个，说张国立是同代演员里演技最差的应该没有问题吧？</span>\n", "        </p>\n", "        <div class=\"comment-report\" data-url=\"https://movie.douban.com/subject/********/?comment_id=3945429647\"></div>\n", "    </div>\n", "    <script>\n", "        (function(){\n", "            $(\"body\").delegate(\".comment-item\", 'mouseenter mouseleave', function (e) {\n", "                switch (e.type) {\n", "                    case \"mouseenter\":\n", "                    $(this).find(\".comment-report\").css('visibility', 'visible');\n", "                    break;\n", "                    case \"mouseleave\":\n", "                    $(this).find(\".comment-report\").css('visibility', 'hidden');\n", "                    break;\n", "                }\n", "            });\n", "        })()\n", "    </script>\n", "\n", "        </div>\n", "        \n", "        <div class=\"comment-item \" data-cid=\"3233654349\">\n", "            \n", "    \n", "    <div class=\"comment\">\n", "        <h3>\n", "            <span class=\"comment-vote\">\n", "                    <span class=\"votes vote-count\">3307</span>\n", "\n", "                    <input value=\"3233654349\" type=\"hidden\"/>\n", "                    <a href=\"javascript:;\" data-id=\"3233654349\"\n", "                        class=\"j a_show_login\" \n", "                        onclick=\"\">有用</a>\n", "                    \n", "                <!-- 删除短评 -->\n", "            </span>\n", "            <span class=\"comment-info\">\n", "                <a href=\"https://www.douban.com/people/199066137/\" class=\"\">青绿的流水</a>\n", "                    <span>看过</span>\n", "                    <span class=\"allstar30 rating\" title=\"还行\"></span>\n", "                <span class=\"comment-time \" title=\"2023-09-28 20:30:05\">\n", "                    2023-09-28 20:30:05\n", "                </span>\n", "                <span class=\"comment-location\">四川</span>\n", "            </span>\n", "        </h3>\n", "        <p class=\" comment-content\">\n", "            \n", "                <span class=\"short\">郑副市长的岳父，那位没有出现过的何老爷子，就是官上之官，不动如山之人吧？</span>\n", "        </p>\n", "        <div class=\"comment-report\" data-url=\"https://movie.douban.com/subject/********/?comment_id=3233654349\"></div>\n", "    </div>\n", "    <script>\n", "        (function(){\n", "            $(\"body\").delegate(\".comment-item\", 'mouseenter mouseleave', function (e) {\n", "                switch (e.type) {\n", "                    case \"mouseenter\":\n", "                    $(this).find(\".comment-report\").css('visibility', 'visible');\n", "                    break;\n", "                    case \"mouseleave\":\n", "                    $(this).find(\".comment-report\").css('visibility', 'hidden');\n", "                    break;\n", "                }\n", "            });\n", "        })()\n", "    </script>\n", "\n", "        </div>\n", "        \n", "        <div class=\"comment-item \" data-cid=\"3895690423\">\n", "            \n", "    \n", "    <div class=\"comment\">\n", "        <h3>\n", "            <span class=\"comment-vote\">\n", "                    <span class=\"votes vote-count\">3968</span>\n", "\n", "                    <input value=\"3895690423\" type=\"hidden\"/>\n", "                    <a href=\"javascript:;\" data-id=\"3895690423\"\n", "                        class=\"j a_show_login\" \n", "                        onclick=\"\">有用</a>\n", "                    \n", "                <!-- 删除短评 -->\n", "            </span>\n", "            <span class=\"comment-info\">\n", "                <a href=\"https://www.douban.com/people/165132929/\" class=\"\">奶茶不甜</a>\n", "                    <span>看过</span>\n", "                    <span class=\"allstar40 rating\" title=\"推荐\"></span>\n", "                <span class=\"comment-time \" title=\"2023-09-28 20:07:56\">\n", "                    2023-09-28 20:07:56\n", "                </span>\n", "                <span class=\"comment-location\">湖北</span>\n", "            </span>\n", "        </h3>\n", "        <p class=\" comment-content\">\n", "            \n", "                <span class=\"short\">于和伟这演技也是没谁了。个个老男人处心积虑，堪称现代版宫斗戏啊。牛逼</span>\n", "        </p>\n", "        <div class=\"comment-report\" data-url=\"https://movie.douban.com/subject/********/?comment_id=3895690423\"></div>\n", "    </div>\n", "    <script>\n", "        (function(){\n", "            $(\"body\").delegate(\".comment-item\", 'mouseenter mouseleave', function (e) {\n", "                switch (e.type) {\n", "                    case \"mouseenter\":\n", "                    $(this).find(\".comment-report\").css('visibility', 'visible');\n", "                    break;\n", "                    case \"mouseleave\":\n", "                    $(this).find(\".comment-report\").css('visibility', 'hidden');\n", "                    break;\n", "                }\n", "            });\n", "        })()\n", "    </script>\n", "\n", "        </div>\n", "        \n", "        <div class=\"comment-item \" data-cid=\"3945400760\">\n", "            \n", "    \n", "    <div class=\"comment\">\n", "        <h3>\n", "            <span class=\"comment-vote\">\n", "                    <span class=\"votes vote-count\">9014</span>\n", "\n", "                    <input value=\"3945400760\" type=\"hidden\"/>\n", "                    <a href=\"javascript:;\" data-id=\"3945400760\"\n", "                        class=\"j a_show_login\" \n", "                        onclick=\"\">有用</a>\n", "                    \n", "                <!-- 删除短评 -->\n", "            </span>\n", "            <span class=\"comment-info\">\n", "                <a href=\"https://www.douban.com/people/162497184/\" class=\"\">豆豆豆豆豆豆变</a>\n", "                    <span>看过</span>\n", "                    <span class=\"allstar20 rating\" title=\"较差\"></span>\n", "                <span class=\"comment-time \" title=\"2023-09-28 20:13:42\">\n", "                    2023-09-28 20:13:42\n", "                </span>\n", "                <span class=\"comment-location\">湖南</span>\n", "            </span>\n", "        </h3>\n", "        <p class=\" comment-content\">\n", "            \n", "                <span class=\"short\">周冬雨和雷佳音不像谈过，倒是张国立和于和伟看起来虐恋情深</span>\n", "        </p>\n", "        <div class=\"comment-report\" data-url=\"https://movie.douban.com/subject/********/?comment_id=3945400760\"></div>\n", "    </div>\n", "    <script>\n", "        (function(){\n", "            $(\"body\").delegate(\".comment-item\", 'mouseenter mouseleave', function (e) {\n", "                switch (e.type) {\n", "                    case \"mouseenter\":\n", "                    $(this).find(\".comment-report\").css('visibility', 'visible');\n", "                    break;\n", "                    case \"mouseleave\":\n", "                    $(this).find(\".comment-report\").css('visibility', 'hidden');\n", "                    break;\n", "                }\n", "            });\n", "        })()\n", "    </script>\n", "\n", "        </div>\n", "\n", "\n", "\n", "\n", "                \n", "                    &gt; <a href=\"comments?sort=new_score&status=P\" >\n", "                        更多短评\n", "                            77647条\n", "                    </a>\n", "        </div>\n", "        <div id=\"new-comments\" class=\"tab\">\n", "            <div id=\"normal\">\n", "            </div>\n", "            <div class=\"fold-hd hide\">\n", "                <a class=\"qa\" href=\"/help/opinion#t2-q0\" target=\"_blank\">为什么被折叠？</a>\n", "                <a class=\"btn-unfold\" href=\"#\">有一些短评被折叠了</a>\n", "                <div class=\"qa-tip\">\n", "                    评论被折叠，是因为发布这条评论的账号行为异常。评论仍可以被展开阅读，对发布人的账号不造成其他影响。如果认为有问题，可以<a href=\"https://help.douban.com/help/ask?category=movie\">联系</a>豆瓣电影。\n", "                </div>\n", "            </div>\n", "            <div class=\"fold-bd\">\n", "            </div>\n", "            <span id=\"total-num\"></span>\n", "        </div>\n", "        <div id=\"following-comments\" class=\"tab\">\n", "            \n", "    \n", "\n", "\n", "\n", "\n", "        <div class=\"comment-item\">\n", "            你关注的人还没写过短评\n", "        </div>\n", "\n", "        </div>\n", "    </div>\n", "    \n", "\n", "\n", "\n", "            <script src=\"https://img1.doubanio.com/f/movie/6eba6f43fb7592ab783e390f654c0d6a96b1598e/dist/movie/subject-comments/comments-section.js\"></script>\n", "            <script>\n", "                $(function () {\n", "                    if (window.SUBJECT_COMMENTS_SECTION) {\n", "                        // tab handler\n", "                        SUBJECT_COMMENTS_SECTION.createTabHandler();\n", "                        // expand handler\n", "                        SUBJECT_COMMENTS_SECTION.createExpandHandler({\n", "                            root: document.getElementById('comments-section'),\n", "                        });\n", "                        // vote handler\n", "                        SUBJECT_COMMENTS_SECTION.createVoteHandler({\n", "                            api: '/j/comment/vote',\n", "                            root: document.getElementById('comments-section'),\n", "                            voteSelector: '.vote-comment',\n", "                            textSelector: '.vote-count',\n", "                            is_released: \"true\",\n", "                            alert_text: \"该电影还未上映，不能投票噢\",\n", "                            afterVote: function (elem) {\n", "                                var parentNode = elem.parentNode;\n", "                                var successElem = document.createElement('span');\n", "                                successElem.innerHTML = '已投票';\n", "                                parentNode.removeChild(elem);\n", "                                parentNode.appendChild(successElem);\n", "                            }\n", "                        });\n", "                    }\n", "                });\n", "            </script>\n", "        </div>\n", "    </div>\n", "\n", "\n", "\n", "<!--        此处是挂载其他页面，不是注释！不是注释！不是注释！-->\n", "        \n", "\n", "\n", "<link rel=\"stylesheet\" href=\"https://img1.doubanio.com/misc/mixed_static/610438fbda6eb614.css\">\n", "\n", "    <section id=\"reviews-wrapper\" class=\"reviews mod movie-content\">\n", "        <header>\n", "            \n", "                <a href=\"new_review\" rel=\"nofollow\" class=\"create-review comment_btn \"\n", "                    data-isverify=\"False\"\n", "                    data-verify-url=\"https://www.douban.com/accounts/phone/verify?redir=http://movie.douban.com/subject/********/new_review\">\n", "                    <span>我要写影评</span>\n", "                </a>\n", "            <h2>\n", "                    坚如磐石的影评 · · · · · ·\n", "\n", "                    <span class=\"pl\">( <a href=\"reviews\">全部 878 条</a> )</span>\n", "            </h2>\n", "        </header>\n", "        \n", "            \n", "            <div class=\"review_filter\">\n", "                            <a href=\"javascript:;;\" class=\"cur\" data-sort=\"\">热门</a>\n", "                            <a href=\"javascript:;;\" data-sort=\"time\">最新</a>\n", "                            <a href=\"javascript:;;\" data-sort=\"follow\">好友</a>\n", "            </div>\n", "            <script>\n", "                var cur_sort = '';\n", "                $('#reviews-wrapper .review_filter a').on('click', function () {\n", "                    var sort = $(this).data('sort');\n", "                    if(sort === cur_sort) return;\n", "\n", "                    if(sort === 'follow' && true){\n", "                        window.location.href = '//www.douban.com/accounts/login?source=movie';\n", "                        return;\n", "                    }\n", "\n", "                    if($('#reviews-wrapper .review_filter').data('doing')) return;\n", "                    $('#reviews-wrapper .review_filter').data('doing', true);\n", "\n", "                    cur_sort = sort;\n", "\n", "                    $('#reviews-wrapper .review_filter a').removeClass('cur');\n", "                    $(this).addClass('cur');\n", "\n", "                    $.getJSON('reviews', { sort: sort }, function(res) {\n", "                        $('#reviews-wrapper .review-list').remove();\n", "                        $('#reviews-wrapper [href=\"reviews?sort=follow\"]').parent().remove();\n", "                        $('#reviews-wrapper .review_filter').after(res.html);\n", "                        $('#reviews-wrapper .review_filter').data('doing', false);\n", "                        $('#reviews-wrapper .review_filter').removeData('doing');\n", "\n", "                        if (res.count === 0) {\n", "                            $('#reviews-wrapper .review-list').html('<span class=\"no-review\">你关注的人还没写过长评</span>');\n", "                        }\n", "                    });\n", "                });\n", "            </script>\n", "\n", "\n", "            \n", "\n", "\n", "\n", "<div class=\"review-list  \">\n", "        \n", "    \n", "\n", "            \n", "    \n", "    <div data-cid=\"15477665\">\n", "        <div class=\"main review-item\" id=\"15477665\">\n", "\n", "            \n", "    \n", "    <header class=\"main-hd\">\n", "        <a href=\"https://www.douban.com/people/longzheking/\" class=\"avator\">\n", "            <img width=\"24\" height=\"24\" src=\"https://img1.doubanio.com/icon/u4285566-30.jpg\">\n", "        </a>\n", "\n", "        <a href=\"https://www.douban.com/people/longzheking/\" class=\"name\">汪金卫</a>\n", "\n", "            <span class=\"allstar40 main-title-rating\" title=\"推荐\"></span>\n", "\n", "        <span content=\"2023-09-30\" class=\"main-meta\">2023-09-30 18:44:35</span>\n", "\n", "\n", "    </header>\n", "\n", "\n", "            <div class=\"main-bd\">\n", "\n", "                <h2><a href=\"https://movie.douban.com/review/15477665/\">从官方公布物料中，还原一些公映版没有的情节</a></h2>\n", "\n", "                <div id=\"review_15477665_short\" class=\"review-short\" data-rid=\"15477665\">\n", "                    <div class=\"short-content\">\n", "                            <p class=\"spoiler-tip\">这篇影评可能有剧透</p>\n", "\n", "                        看完《坚如磐石》公映版后，心里有点堵得慌。尽管公映版展示了不俗的尺度、画面光影风格和官场现实指涉。但不可否认，删减痕迹仍然让人感觉疑惑大于领悟，问号多于叹号。诸多的剧情需要在残存的细节中窥见、猜测原初的样貌。太多的不可说需要深挖。郑刚和妻子的角色扑朔迷离，...\n", "\n", "                        &nbsp;(<a href=\"javascript:;\" id=\"toggle-15477665-copy\" class=\"unfold\" title=\"展开\">展开</a>)\n", "                    </div>\n", "                </div>\n", "\n", "                <div id=\"review_15477665_full\" class=\"hidden\">\n", "                    <div id=\"review_15477665_full_content\" class=\"full-content\"></div>\n", "                </div>\n", "\n", "                <div class=\"action\">\n", "                    <a href=\"javascript:;\" class=\"action-btn up\" data-rid=\"15477665\" title=\"有用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/536fd337139250b5fb3cf9e79cb65c6193f8b20b/pics/up.png\" />\n", "                        <span id=\"r-useful_count-15477665\">\n", "                                643\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"javascript:;\" class=\"action-btn down\" data-rid=\"15477665\" title=\"没用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/68849027911140623cf338c9845893c4566db851/pics/down.png\" />\n", "                        <span id=\"r-useless_count-15477665\">\n", "                                12\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"https://movie.douban.com/review/15477665/#comments\" class=\"reply \">98回应</a>\n", "\n", "                    <a href=\"javascript:;;\" class=\"fold hidden\">收起</a>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "\n", "            \n", "    \n", "    <div data-cid=\"15472493\">\n", "        <div class=\"main review-item\" id=\"15472493\">\n", "\n", "            \n", "    \n", "    <header class=\"main-hd\">\n", "        <a href=\"https://www.douban.com/people/40760493/\" class=\"avator\">\n", "            <img width=\"24\" height=\"24\" src=\"https://img1.doubanio.com/icon/u40760493-8.jpg\">\n", "        </a>\n", "\n", "        <a href=\"https://www.douban.com/people/40760493/\" class=\"name\">一朋</a>\n", "\n", "            <span class=\"allstar50 main-title-rating\" title=\"力荐\"></span>\n", "\n", "        <span content=\"2023-09-27\" class=\"main-meta\">2023-09-27 21:44:48</span>\n", "\n", "\n", "    </header>\n", "\n", "\n", "            <div class=\"main-bd\">\n", "\n", "                <h2><a href=\"https://movie.douban.com/review/15472493/\">为什么《教父》里麦克会为了家人做黑帮，而《坚如磐石》里苏见明选择大义灭亲？</a></h2>\n", "\n", "                <div id=\"review_15472493_short\" class=\"review-short\" data-rid=\"15472493\">\n", "                    <div class=\"short-content\">\n", "                            <p class=\"spoiler-tip\">这篇影评可能有剧透</p>\n", "\n", "                        今天中午12点05在万达在看的这个电影，看完之后我就在想：为什么同样是黑帮题材，《教父》的主角麦克会为了家人而宁愿做黑帮，而《坚如磐石》的主角苏见明则会为了正义而要大义灭亲？我想答案可能只有一个，苏见明的觉悟比较高，符合社会主义核心价值观。这个电影再次明证：西...\n", "\n", "                        &nbsp;(<a href=\"javascript:;\" id=\"toggle-15472493-copy\" class=\"unfold\" title=\"展开\">展开</a>)\n", "                    </div>\n", "                </div>\n", "\n", "                <div id=\"review_15472493_full\" class=\"hidden\">\n", "                    <div id=\"review_15472493_full_content\" class=\"full-content\"></div>\n", "                </div>\n", "\n", "                <div class=\"action\">\n", "                    <a href=\"javascript:;\" class=\"action-btn up\" data-rid=\"15472493\" title=\"有用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/536fd337139250b5fb3cf9e79cb65c6193f8b20b/pics/up.png\" />\n", "                        <span id=\"r-useful_count-15472493\">\n", "                                612\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"javascript:;\" class=\"action-btn down\" data-rid=\"15472493\" title=\"没用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/68849027911140623cf338c9845893c4566db851/pics/down.png\" />\n", "                        <span id=\"r-useless_count-15472493\">\n", "                                79\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"https://movie.douban.com/review/15472493/#comments\" class=\"reply \">258回应</a>\n", "\n", "                    <a href=\"javascript:;;\" class=\"fold hidden\">收起</a>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "\n", "            \n", "    \n", "    <div data-cid=\"15474427\">\n", "        <div class=\"main review-item\" id=\"15474427\">\n", "\n", "            \n", "    \n", "    <header class=\"main-hd\">\n", "        <a href=\"https://www.douban.com/people/175211301/\" class=\"avator\">\n", "            <img width=\"24\" height=\"24\" src=\"https://img1.doubanio.com/icon/u175211301-7.jpg\">\n", "        </a>\n", "\n", "        <a href=\"https://www.douban.com/people/175211301/\" class=\"name\">goswing</a>\n", "\n", "            <span class=\"allstar10 main-title-rating\" title=\"很差\"></span>\n", "\n", "        <span content=\"2023-09-28\" class=\"main-meta\">2023-09-28 22:32:15</span>\n", "\n", "\n", "    </header>\n", "\n", "\n", "            <div class=\"main-bd\">\n", "\n", "                <h2><a href=\"https://movie.douban.com/review/15474427/\">如坐针毡如芒刺背</a></h2>\n", "\n", "                <div id=\"review_15474427_short\" class=\"review-short\" data-rid=\"15474427\">\n", "                    <div class=\"short-content\">\n", "                            <p class=\"spoiler-tip\">这篇影评可能有剧透</p>\n", "\n", "                        本人已经近视了，是什么电影让我看到一半把眼镜摘了下来…… 我以为的是：逻辑缜密、细节完美，经得起推敲的悬疑断案片 真实情况是：一场无辜死亡、资源浪费、上级和下层领导班子陪着一位父亲和他的狗血儿子的一部旷世史诗级狗血灾难片。 一切的一切的一切，浪费那么多警力，死...\n", "\n", "                        &nbsp;(<a href=\"javascript:;\" id=\"toggle-15474427-copy\" class=\"unfold\" title=\"展开\">展开</a>)\n", "                    </div>\n", "                </div>\n", "\n", "                <div id=\"review_15474427_full\" class=\"hidden\">\n", "                    <div id=\"review_15474427_full_content\" class=\"full-content\"></div>\n", "                </div>\n", "\n", "                <div class=\"action\">\n", "                    <a href=\"javascript:;\" class=\"action-btn up\" data-rid=\"15474427\" title=\"有用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/536fd337139250b5fb3cf9e79cb65c6193f8b20b/pics/up.png\" />\n", "                        <span id=\"r-useful_count-15474427\">\n", "                                350\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"javascript:;\" class=\"action-btn down\" data-rid=\"15474427\" title=\"没用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/68849027911140623cf338c9845893c4566db851/pics/down.png\" />\n", "                        <span id=\"r-useless_count-15474427\">\n", "                                26\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"https://movie.douban.com/review/15474427/#comments\" class=\"reply \">153回应</a>\n", "\n", "                    <a href=\"javascript:;;\" class=\"fold hidden\">收起</a>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "\n", "            \n", "    \n", "    <div data-cid=\"15474482\">\n", "        <div class=\"main review-item\" id=\"15474482\">\n", "\n", "            \n", "    \n", "    <header class=\"main-hd\">\n", "        <a href=\"https://www.douban.com/people/171051963/\" class=\"avator\">\n", "            <img width=\"24\" height=\"24\" src=\"https://img1.doubanio.com/icon/u171051963-8.jpg\">\n", "        </a>\n", "\n", "        <a href=\"https://www.douban.com/people/171051963/\" class=\"name\">知识原子</a>\n", "\n", "            <span class=\"allstar50 main-title-rating\" title=\"力荐\"></span>\n", "\n", "        <span content=\"2023-09-28\" class=\"main-meta\">2023-09-28 22:47:54</span>\n", "\n", "\n", "    </header>\n", "\n", "\n", "            <div class=\"main-bd\">\n", "\n", "                <h2><a href=\"https://movie.douban.com/review/15474482/\">简单推测原版（新增官方物料实锤）</a></h2>\n", "\n", "                <div id=\"review_15474482_short\" class=\"review-short\" data-rid=\"15474482\">\n", "                    <div class=\"short-content\">\n", "                            <p class=\"spoiler-tip\">这篇影评可能有剧透</p>\n", "\n", "                        看完走出来，听到一起走出来的一对情侣观众讨论电影，女生觉得很好看，感觉每个人都有很多值得咂摸的，男生批评：好电影不能光靠观众想象，它得拍出来啊！为什么不拍出来？ emmm，有没有可能它曾经拍出来了？！ 不敢相信这是一部被审查反复修改调整了四年的电影，因为它的完成...\n", "\n", "                        &nbsp;(<a href=\"javascript:;\" id=\"toggle-15474482-copy\" class=\"unfold\" title=\"展开\">展开</a>)\n", "                    </div>\n", "                </div>\n", "\n", "                <div id=\"review_15474482_full\" class=\"hidden\">\n", "                    <div id=\"review_15474482_full_content\" class=\"full-content\"></div>\n", "                </div>\n", "\n", "                <div class=\"action\">\n", "                    <a href=\"javascript:;\" class=\"action-btn up\" data-rid=\"15474482\" title=\"有用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/536fd337139250b5fb3cf9e79cb65c6193f8b20b/pics/up.png\" />\n", "                        <span id=\"r-useful_count-15474482\">\n", "                                338\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"javascript:;\" class=\"action-btn down\" data-rid=\"15474482\" title=\"没用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/68849027911140623cf338c9845893c4566db851/pics/down.png\" />\n", "                        <span id=\"r-useless_count-15474482\">\n", "                                23\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"https://movie.douban.com/review/15474482/#comments\" class=\"reply \">130回应</a>\n", "\n", "                    <a href=\"javascript:;;\" class=\"fold hidden\">收起</a>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "\n", "            \n", "    \n", "    <div data-cid=\"15478912\">\n", "        <div class=\"main review-item\" id=\"15478912\">\n", "\n", "            \n", "    \n", "    <header class=\"main-hd\">\n", "        <a href=\"https://www.douban.com/people/46843344/\" class=\"avator\">\n", "            <img width=\"24\" height=\"24\" src=\"https://img1.doubanio.com/icon/u46843344-18.jpg\">\n", "        </a>\n", "\n", "        <a href=\"https://www.douban.com/people/46843344/\" class=\"name\">刘康康</a>\n", "\n", "            <span class=\"allstar40 main-title-rating\" title=\"推荐\"></span>\n", "\n", "        <span content=\"2023-10-01\" class=\"main-meta\">2023-10-01 13:01:22</span>\n", "\n", "\n", "    </header>\n", "\n", "\n", "            <div class=\"main-bd\">\n", "\n", "                <h2><a href=\"https://movie.douban.com/review/15478912/\">张艺谋的愚公移山</a></h2>\n", "\n", "                <div id=\"review_15478912_short\" class=\"review-short\" data-rid=\"15478912\">\n", "                    <div class=\"short-content\">\n", "\n", "                        在张艺谋最新上映的电影《坚如磐石》里，雷佳音所扮演的男主角苏见明，多次用“唇语”这个梗，跟周冬雨所扮演的女警李惠琳打趣，让李惠琳猜测摄像头拍下的监控画面里，关键人物之间究竟在进行着什么样的对话。而在电影之外，观众同样需要进行这样一场“解读唇语”的游戏，因为...\n", "\n", "                        &nbsp;(<a href=\"javascript:;\" id=\"toggle-15478912-copy\" class=\"unfold\" title=\"展开\">展开</a>)\n", "                    </div>\n", "                </div>\n", "\n", "                <div id=\"review_15478912_full\" class=\"hidden\">\n", "                    <div id=\"review_15478912_full_content\" class=\"full-content\"></div>\n", "                </div>\n", "\n", "                <div class=\"action\">\n", "                    <a href=\"javascript:;\" class=\"action-btn up\" data-rid=\"15478912\" title=\"有用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/536fd337139250b5fb3cf9e79cb65c6193f8b20b/pics/up.png\" />\n", "                        <span id=\"r-useful_count-15478912\">\n", "                                260\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"javascript:;\" class=\"action-btn down\" data-rid=\"15478912\" title=\"没用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/68849027911140623cf338c9845893c4566db851/pics/down.png\" />\n", "                        <span id=\"r-useless_count-15478912\">\n", "                                3\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"https://movie.douban.com/review/15478912/#comments\" class=\"reply \">10回应</a>\n", "\n", "                    <a href=\"javascript:;;\" class=\"fold hidden\">收起</a>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "\n", "            \n", "    \n", "    <div data-cid=\"15474155\">\n", "        <div class=\"main review-item\" id=\"15474155\">\n", "\n", "            \n", "    \n", "    <header class=\"main-hd\">\n", "        <a href=\"https://www.douban.com/people/36771726/\" class=\"avator\">\n", "            <img width=\"24\" height=\"24\" src=\"https://img9.doubanio.com/icon/u36771726-16.jpg\">\n", "        </a>\n", "\n", "        <a href=\"https://www.douban.com/people/36771726/\" class=\"name\">唐山</a>\n", "\n", "            <span class=\"allstar40 main-title-rating\" title=\"推荐\"></span>\n", "\n", "        <span content=\"2023-09-28\" class=\"main-meta\">2023-09-28 21:06:40</span>\n", "\n", "\n", "    </header>\n", "\n", "\n", "            <div class=\"main-bd\">\n", "\n", "                <h2><a href=\"https://movie.douban.com/review/15474155/\">《坚如磐石》，原以为是反腐片，你却拍出了命运的悲怆</a></h2>\n", "\n", "                <div id=\"review_15474155_short\" class=\"review-short\" data-rid=\"15474155\">\n", "                    <div class=\"short-content\">\n", "\n", "                        如果想看没有一句废话台词、没有一个无意义镜头的电影，那么，就看《坚如磐石》。 如果仍执着于严肃影片与类型影片的区别，那么，就看《坚如磐石》。 如果你内心试图抵抗叙事套路，却又渴望将自我融入到故事中，那么，就看《坚如磐石》。 …… 总之，让我来评过去几年间，看到...\n", "\n", "                        &nbsp;(<a href=\"javascript:;\" id=\"toggle-15474155-copy\" class=\"unfold\" title=\"展开\">展开</a>)\n", "                    </div>\n", "                </div>\n", "\n", "                <div id=\"review_15474155_full\" class=\"hidden\">\n", "                    <div id=\"review_15474155_full_content\" class=\"full-content\"></div>\n", "                </div>\n", "\n", "                <div class=\"action\">\n", "                    <a href=\"javascript:;\" class=\"action-btn up\" data-rid=\"15474155\" title=\"有用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/536fd337139250b5fb3cf9e79cb65c6193f8b20b/pics/up.png\" />\n", "                        <span id=\"r-useful_count-15474155\">\n", "                                466\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"javascript:;\" class=\"action-btn down\" data-rid=\"15474155\" title=\"没用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/68849027911140623cf338c9845893c4566db851/pics/down.png\" />\n", "                        <span id=\"r-useless_count-15474155\">\n", "                                72\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"https://movie.douban.com/review/15474155/#comments\" class=\"reply \">67回应</a>\n", "\n", "                    <a href=\"javascript:;;\" class=\"fold hidden\">收起</a>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "\n", "            \n", "    \n", "    <div data-cid=\"15472398\">\n", "        <div class=\"main review-item\" id=\"15472398\">\n", "\n", "            \n", "    \n", "    <header class=\"main-hd\">\n", "        <a href=\"https://www.douban.com/people/even2even/\" class=\"avator\">\n", "            <img width=\"24\" height=\"24\" src=\"https://img2.doubanio.com/icon/u1612198-2.jpg\">\n", "        </a>\n", "\n", "        <a href=\"https://www.douban.com/people/even2even/\" class=\"name\">Evenc伊文西</a>\n", "\n", "            <span class=\"allstar30 main-title-rating\" title=\"还行\"></span>\n", "\n", "        <span content=\"2023-09-27\" class=\"main-meta\">2023-09-27 20:50:57</span>\n", "\n", "\n", "    </header>\n", "\n", "\n", "            <div class=\"main-bd\">\n", "\n", "                <h2><a href=\"https://movie.douban.com/review/15472398/\">这题材张艺谋也没办法</a></h2>\n", "\n", "                <div id=\"review_15472398_short\" class=\"review-short\" data-rid=\"15472398\">\n", "                    <div class=\"short-content\">\n", "\n", "                        《坚如磐石》是今年国庆档我最期待的电影，没有之一。 这部电影其实完成得很早，跟《县崖之上》前后脚，一直压着未上映，明显受题材所累，肉眼可见地被改动了不少，大概已非预想中的面貌。导演张艺谋在互动时，对改动情况几番欲言又止，但遗憾之情溢于言表，他纠结的甚至不是电...\n", "\n", "                        &nbsp;(<a href=\"javascript:;\" id=\"toggle-15472398-copy\" class=\"unfold\" title=\"展开\">展开</a>)\n", "                    </div>\n", "                </div>\n", "\n", "                <div id=\"review_15472398_full\" class=\"hidden\">\n", "                    <div id=\"review_15472398_full_content\" class=\"full-content\"></div>\n", "                </div>\n", "\n", "                <div class=\"action\">\n", "                    <a href=\"javascript:;\" class=\"action-btn up\" data-rid=\"15472398\" title=\"有用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/536fd337139250b5fb3cf9e79cb65c6193f8b20b/pics/up.png\" />\n", "                        <span id=\"r-useful_count-15472398\">\n", "                                112\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"javascript:;\" class=\"action-btn down\" data-rid=\"15472398\" title=\"没用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/68849027911140623cf338c9845893c4566db851/pics/down.png\" />\n", "                        <span id=\"r-useless_count-15472398\">\n", "                                6\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"https://movie.douban.com/review/15472398/#comments\" class=\"reply \">50回应</a>\n", "\n", "                    <a href=\"javascript:;;\" class=\"fold hidden\">收起</a>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "\n", "            \n", "    \n", "    <div data-cid=\"15480679\">\n", "        <div class=\"main review-item\" id=\"15480679\">\n", "\n", "            \n", "    \n", "    <header class=\"main-hd\">\n", "        <a href=\"https://www.douban.com/people/mumudancing/\" class=\"avator\">\n", "            <img width=\"24\" height=\"24\" src=\"https://img1.doubanio.com/icon/u1553180-97.jpg\">\n", "        </a>\n", "\n", "        <a href=\"https://www.douban.com/people/mumudancing/\" class=\"name\">mumudancing</a>\n", "\n", "            <span class=\"allstar40 main-title-rating\" title=\"推荐\"></span>\n", "\n", "        <span content=\"2023-10-02\" class=\"main-meta\">2023-10-02 14:19:42</span>\n", "\n", "\n", "    </header>\n", "\n", "\n", "            <div class=\"main-bd\">\n", "\n", "                <h2><a href=\"https://movie.douban.com/review/15480679/\">于和伟，附身“惊弓之鸟”</a></h2>\n", "\n", "                <div id=\"review_15480679_short\" class=\"review-short\" data-rid=\"15480679\">\n", "                    <div class=\"short-content\">\n", "                            <p class=\"spoiler-tip\">这篇影评可能有剧透</p>\n", "\n", "                        《坚如磐石》是四年前拍的电影，经过反复删改到今天上映，同类题材网剧《狂飙》大红大紫之后，已经失去了先机。加之“政商勾结”的故事在我们所知环境内必然遭受最为严厉的审查，电影原本的逻辑、人物关系经改动，实已伤筋动骨，如今呈现在大众眼前的这一版本，注定充满遗憾。 ...\n", "\n", "                        &nbsp;(<a href=\"javascript:;\" id=\"toggle-15480679-copy\" class=\"unfold\" title=\"展开\">展开</a>)\n", "                    </div>\n", "                </div>\n", "\n", "                <div id=\"review_15480679_full\" class=\"hidden\">\n", "                    <div id=\"review_15480679_full_content\" class=\"full-content\"></div>\n", "                </div>\n", "\n", "                <div class=\"action\">\n", "                    <a href=\"javascript:;\" class=\"action-btn up\" data-rid=\"15480679\" title=\"有用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/536fd337139250b5fb3cf9e79cb65c6193f8b20b/pics/up.png\" />\n", "                        <span id=\"r-useful_count-15480679\">\n", "                                74\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"javascript:;\" class=\"action-btn down\" data-rid=\"15480679\" title=\"没用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/68849027911140623cf338c9845893c4566db851/pics/down.png\" />\n", "                        <span id=\"r-useless_count-15480679\">\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"https://movie.douban.com/review/15480679/#comments\" class=\"reply \">8回应</a>\n", "\n", "                    <a href=\"javascript:;;\" class=\"fold hidden\">收起</a>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "\n", "            \n", "    \n", "    <div data-cid=\"15474559\">\n", "        <div class=\"main review-item\" id=\"15474559\">\n", "\n", "            \n", "    \n", "    <header class=\"main-hd\">\n", "        <a href=\"https://www.douban.com/people/181101669/\" class=\"avator\">\n", "            <img width=\"24\" height=\"24\" src=\"https://img2.doubanio.com/icon/u181101669-3.jpg\">\n", "        </a>\n", "\n", "        <a href=\"https://www.douban.com/people/181101669/\" class=\"name\">黑杂WDXY</a>\n", "\n", "            <span class=\"allstar30 main-title-rating\" title=\"还行\"></span>\n", "\n", "        <span content=\"2023-09-28\" class=\"main-meta\">2023-09-28 23:12:43</span>\n", "\n", "\n", "    </header>\n", "\n", "\n", "            <div class=\"main-bd\">\n", "\n", "                <h2><a href=\"https://movie.douban.com/review/15474559/\">看完脑子里有很多问号的，希望能解决大家一点点剧情上的疑惑</a></h2>\n", "\n", "                <div id=\"review_15474559_short\" class=\"review-short\" data-rid=\"15474559\">\n", "                    <div class=\"short-content\">\n", "                            <p class=\"spoiler-tip\">这篇影评可能有剧透</p>\n", "\n", "                        （纯主观，没看过原版，瞎猜） 1.雷佳音凭什么能找到手机？ 答：因为他年轻看过“天线宝宝” 外甥女打电话告诉他找到手机时，还说了一句话：“小波”知道。 “小波”就是那个红色的天线宝宝 不排除外甥女为了一千万已经把手机交出来了，只是于和伟又把手机重新放回去了。 因为...\n", "\n", "                        &nbsp;(<a href=\"javascript:;\" id=\"toggle-15474559-copy\" class=\"unfold\" title=\"展开\">展开</a>)\n", "                    </div>\n", "                </div>\n", "\n", "                <div id=\"review_15474559_full\" class=\"hidden\">\n", "                    <div id=\"review_15474559_full_content\" class=\"full-content\"></div>\n", "                </div>\n", "\n", "                <div class=\"action\">\n", "                    <a href=\"javascript:;\" class=\"action-btn up\" data-rid=\"15474559\" title=\"有用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/536fd337139250b5fb3cf9e79cb65c6193f8b20b/pics/up.png\" />\n", "                        <span id=\"r-useful_count-15474559\">\n", "                                113\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"javascript:;\" class=\"action-btn down\" data-rid=\"15474559\" title=\"没用\">\n", "                        <img src=\"https://img1.doubanio.com/f/zerkalo/68849027911140623cf338c9845893c4566db851/pics/down.png\" />\n", "                        <span id=\"r-useless_count-15474559\">\n", "                                4\n", "                        </span>\n", "                    </a>\n", "                    <a href=\"https://movie.douban.com/review/15474559/#comments\" class=\"reply \">83回应</a>\n", "\n", "                    <a href=\"javascript:;;\" class=\"fold hidden\">收起</a>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "\n", "\n", "\n", "\n", "    \n", "\n", "    <!-- COLLECTED JS -->\n", "    <!-- COLLECTED CSS -->\n", "</div>\n", "\n", "    <script type=\"text/javascript\">\n", "        (function() {\n", "            if (window.__init_review_list) return;\n", "            __init_review_list = true;\n", "                window.is_released = true\n", "                window.txt_released = '该电影还未上映，不能投票噢'\n", "        })();\n", "        window.useful_icon = \"https://img1.doubanio.com/f/zerkalo/536fd337139250b5fb3cf9e79cb65c6193f8b20b/pics/up.png\";\n", "        window.usefuled_icon = \"https://img1.doubanio.com/f/zerkalo/635290bb14771c97270037be21ad50514d57acc3/pics/up-full.png\";\n", "        window.useless_icon = \"https://img1.doubanio.com/f/zerkalo/68849027911140623cf338c9845893c4566db851/pics/down.png\";\n", "        window.uselessed_icon = \"https://img1.doubanio.com/f/zerkalo/23cee7343568ca814238f5ef18bf8aadbe959df2/pics/down-full.png\";\n", "    </script>\n", "\n", "    <link rel=\"stylesheet\" href=\"https://img1.doubanio.com/f/zerkalo/3aeb281ab0e4f2c7050458684acfeb6838441de9/css/review/editor/ng/setting_standalone.css\">\n", "    <script src=\"https://img1.doubanio.com/f/zerkalo/938cdbe2e223a3117cbbcb4929cae2001b402c20/js/review/editor/ng/manifest.js\"></script>\n", "    <script src=\"https://img1.doubanio.com/f/zerkalo/296cd5fec472a78add5fee958c58d72f47d91586/js/review/editor/ng/vendor.js\"></script>\n", "    <script src=\"https://img1.doubanio.com/f/zerkalo/8a6f99feb702ed3c956a552c1b978285dcfdf7d9/js/review/editor/ng/setting_standalone.js\"></script>\n", "    <script src=\"https://img1.doubanio.com/f/zerkalo/8941af7854ddad9561648b706cdb49f3d1534ff3/js/review/editor/ng/render_gif.js\"></script>\n", "    <script src=\"https://img1.doubanio.com/f/zerkalo/ad32b4b739a59a90e9e3437fd2fba797dd7c26f2/js/review/actions.js\"></script>\n", "    <script src=\"https://img1.doubanio.com/f/zerkalo/7196bdec780f03785f55b06fda34999595057f65/js/review/unfold.js\"></script>\n", "    <script src=\"https://img1.doubanio.com/f/vendors/f25ae221544f39046484a823776f3aa01769ee10/js/ui/dialog.js\"></script>\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "                <p class=\"pl\">\n", "                    &gt;\n", "                        <a href=\"reviews\">\n", "                            更多影评\n", "                                878篇\n", "                        </a>\n", "                </p>\n", "    </section>\n", "<!-- COLLECTED JS -->\n", "\n", "\n", "    <br/>\n", "\n", "    \n", "                \n", "    <div class=\"section-discussion\">\n", "        <div class=\"hd-ops\">\n", "            <a class=\"comment_btn j a_show_login\" href=\"https://www.douban.com/register?reason=discussion\" rel=\"nofollow\"><span>发起新的讨论</span></a>\n", "        </div>\n", "        <div class=\"mod-hd\">\n", "            \n", "    <h2>\n", "        小组讨论\n", "         &nbsp; &middot;&nbsp; &middot;&nbsp; &middot;&nbsp; &middot;&nbsp; &middot;&nbsp; &middot;\n", "    </h2>\n", "\n", "        </div>\n", "        \n", "  <table class=\"olt\"><tr><td><td><td><td></tr>\n", "        \n", "        <tr>\n", "          <td class=\"pl\"><a href=\"https://www.douban.com/group/topic/295637454/\" title=\"细思极恐的地方\">细思极恐的地方</a></td>\n", "          <td class=\"pl\"><span>来自</span><a href=\"https://www.douban.com/people/230170884/\">BH0786</a></td>\n", "          <td class=\"pl\"><span>19 回应</span></td>\n", "          <td class=\"pl\"><span>2023-10-08 14:32:08</span></td>\n", "        </tr>\n", "        \n", "        <tr>\n", "          <td class=\"pl\"><a href=\"https://www.douban.com/group/topic/295634617/\" title=\"《坚如磐石》很好看，看不懂的欢迎聊聊\">《坚如磐石》很好看，看不懂的欢迎聊聊</a></td>\n", "          <td class=\"pl\"><span>来自</span><a href=\"https://www.douban.com/people/37130609/\">艾白</a></td>\n", "          <td class=\"pl\"><span>149 回应</span></td>\n", "          <td class=\"pl\"><span>2023-10-08 14:29:47</span></td>\n", "        </tr>\n", "        \n", "        <tr>\n", "          <td class=\"pl\"><a href=\"https://www.douban.com/group/topic/295830785/\" title=\"难以置信这电影评论这么低！可能是我太肤浅了！\">难以置信这电影评论这么低！可能是我太肤浅了！</a></td>\n", "          <td class=\"pl\"><span>来自</span><a href=\"https://www.douban.com/people/143899737/\">ansealeral</a></td>\n", "          <td class=\"pl\"><span>16 回应</span></td>\n", "          <td class=\"pl\"><span>2023-10-08 14:25:28</span></td>\n", "        </tr>\n", "        \n", "        <tr>\n", "          <td class=\"pl\"><a href=\"https://www.douban.com/group/topic/295772831/\" title=\"黎志田是一款。。。女王受。。。我嬷起来了\">黎志田是一款。。。女王受。。。我嬷起来了</a></td>\n", "          <td class=\"pl\"><span>来自</span><a href=\"https://www.douban.com/people/185801550/\">momo</a></td>\n", "          <td class=\"pl\"><span>14 回应</span></td>\n", "          <td class=\"pl\"><span>2023-10-08 14:16:10</span></td>\n", "        </tr>\n", "        \n", "        <tr>\n", "          <td class=\"pl\"><a href=\"https://www.douban.com/group/topic/295646616/\" title=\"惠琳开枪还被砍死那一段真的很莫名其妙\">惠琳开枪还被砍死那一段真的很莫名其妙</a></td>\n", "          <td class=\"pl\"><span>来自</span><a href=\"https://www.douban.com/people/4468522/\">emmar<PERSON>ee</a></td>\n", "          <td class=\"pl\"><span>18 回应</span></td>\n", "          <td class=\"pl\"><span>2023-10-08 14:07:56</span></td>\n", "        </tr>\n", "        \n", "        <tr>\n", "          <td class=\"pl\"><a href=\"https://www.douban.com/group/topic/295731469/\" title=\"黎志田这种人是怎么成为当地首富的\">黎志田这种人是怎么成为当地首富的</a></td>\n", "          <td class=\"pl\"><span>来自</span><a href=\"https://www.douban.com/people/154289992/\">gztcsxy2016</a></td>\n", "          <td class=\"pl\"><span>12 回应</span></td>\n", "          <td class=\"pl\"><span>2023-10-08 13:45:19</span></td>\n", "        </tr>\n", "        \n", "        <tr>\n", "          <td class=\"pl\"><a href=\"https://www.douban.com/group/topic/295630781/\" title=\"为什么说杨小薇是情人\">为什么说杨小薇是情人</a></td>\n", "          <td class=\"pl\"><span>来自</span><a href=\"https://www.douban.com/people/192716273/\">momo</a></td>\n", "          <td class=\"pl\"><span>73 回应</span></td>\n", "          <td class=\"pl\"><span>2023-10-08 13:35:04</span></td>\n", "        </tr>\n", "        \n", "        <tr>\n", "          <td class=\"pl\"><a href=\"https://www.douban.com/group/topic/295645508/\" title=\"我记得首映式上，张国立吞吞吐吐的讲了这段话，有助于我们理解剧情\">我记得首映式上，张国立吞吞吐吐的讲了这段话，有...</a></td>\n", "          <td class=\"pl\"><span>来自</span><a href=\"https://www.douban.com/people/207569507/\">豆友207569507</a></td>\n", "          <td class=\"pl\"><span>114 回应</span></td>\n", "          <td class=\"pl\"><span>2023-10-08 13:27:30</span></td>\n", "        </tr>\n", "        \n", "        <tr>\n", "          <td class=\"pl\"><a href=\"https://www.douban.com/group/topic/295597505/\" title=\"莫名其妙的人物及动机\">莫名其妙的人物及动机</a></td>\n", "          <td class=\"pl\"><span>来自</span><a href=\"https://www.douban.com/people/220609541/\">神经蛙</a></td>\n", "          <td class=\"pl\"><span>50 回应</span></td>\n", "          <td class=\"pl\"><span>2023-10-08 13:24:17</span></td>\n", "        </tr>\n", "        \n", "        <tr>\n", "          <td class=\"pl\"><a href=\"https://www.douban.com/group/topic/295615217/\" title=\"黎志田行刑前，他的女儿都拒绝去看他，这是想表达什么？？\">黎志田行刑前，他的女儿都拒绝去看他，这是想表达...</a></td>\n", "          <td class=\"pl\"><span>来自</span><a href=\"https://www.douban.com/people/137001303/\">小白白</a></td>\n", "          <td class=\"pl\"><span>143 回应</span></td>\n", "          <td class=\"pl\"><span>2023-10-08 13:21:25</span></td>\n", "        </tr>\n", "  </table>\n", "\n", "        \n", "        <p class=\"pl\" align=\"right\">\n", "            <a href=\"//www.douban.com/group/739721#topics\" rel=\"nofollow\">\n", "                &gt; 去这部电影的小组讨论（全部760条）\n", "            </a>\n", "        </p>\n", "    </div>\n", "\n", "\n", "\n", "\n", "            </div>\n", "            <div class=\"aside\">\n", "                \n", "\n", "\n", "    \n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "            \n", "\n", "\n", "\n", "\n", "\n", "\n", "    <div class=\"ticket\">\n", "        <a class=\"ticket-btn\" href=\"https://movie.douban.com/ticket/redirect/?movie_id=********\">购票</a>\n", "    </div>\n", "\n", "\n", "\n", "    <!-- douban ad begin -->\n", "    <div id=\"dale_movie_subject_top_right\"></div>\n", "    <!-- douban ad end -->\n", "\n", "    \n", "\n", "\n", "\n", "<style type=\"text/css\">\n", "    .m4 {margin-bottom:8px; padding-bottom:8px;}\n", "    .movieOnline {background:#FFF6ED; padding:10px; margin-bottom:20px;}\n", "    .movieOnline h2 {margin:0 0 5px;}\n", "    .movieOnline .sitename {line-height:2em; width:160px;}\n", "    .movieOnline td,.movieOnline td a:link,.movieOnline td a:visited{color:#666;}\n", "    .movieOnline td a:hover {color:#fff;}\n", "    .link-bt:link,\n", "    .link-bt:visited,\n", "    .link-bt:hover,\n", "    .link-bt:active {margin:5px 0 0; padding:2px 8px; background:#a8c598; color:#fff; -moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px; display:inline-block;}\n", "</style>\n", "\n", "\n", "\n", "    \n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "<!--tags-->\n", "\n", "    <div id=\"dale_subject_right_guess_you_like\"></div>\n", "    <div id=\"dale_movie_subject_inner_middle\"></div>\n", "    \n", "        \n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "<div id=\"subject-doulist\">\n", "    \n", "    \n", "    <h2>\n", "        <i class=\"\">以下片单推荐</i>\n", "              · · · · · ·\n", "            <span class=\"pl\">\n", "            (\n", "                <a href=\"https://movie.douban.com/subject/********/doulists\">全部</a>\n", "            )\n", "            </span>\n", "    </h2>\n", "\n", "\n", "    \n", "    <ul>\n", "            \n", "                <li>\n", "                    <a href=\"https://www.douban.com/doulist/13712178/\" target=\"_blank\">评价人数超过十万的电影</a>\n", "                    <span>(依然饭特稀)</span>\n", "                </li>\n", "            \n", "                <li>\n", "                    <a href=\"https://www.douban.com/doulist/1434921/\" target=\"_blank\">2023—2025值得关注的华语电影</a>\n", "                    <span>(closer)</span>\n", "                </li>\n", "            \n", "                <li>\n", "                    <a href=\"https://www.douban.com/doulist/1817142/\" target=\"_blank\">豆瓣评价人数过十万的影片〖国产篇〗</a>\n", "                    <span>(<PERSON>)</span>\n", "                </li>\n", "            \n", "                <li>\n", "                    <a href=\"https://www.douban.com/doulist/745193/\" target=\"_blank\">【暴裂无声】</a>\n", "                    <span>(罗弘霉素)</span>\n", "                </li>\n", "            \n", "                <li>\n", "                    <a href=\"https://www.douban.com/doulist/1964161/\" target=\"_blank\">这些电影，可以期待</a>\n", "                    <span>(明天就现充)</span>\n", "                </li>\n", "    </ul>\n", "\n", "</div>\n", "\n", "    \n", "        \n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "<div id=\"subject-others-interests\">\n", "    \n", "    \n", "    <h2>\n", "        <i class=\"\">谁在看这部电影</i>\n", "              · · · · · ·\n", "    </h2>\n", "\n", "    \n", "    <ul class=\"\">\n", "            \n", "            <li class=\"\">\n", "                <a href=\"https://www.douban.com/people/259164949/\" class=\"others-interest-avatar\">\n", "                    <img src=\"https://img2.doubanio.com/icon/u259164949-1.jpg\" class=\"pil\" alt=\"勿扰\">\n", "                </a>\n", "                <div class=\"others-interest-info\">\n", "                    <a href=\"https://www.douban.com/people/259164949/\" class=\"\">勿扰</a>\n", "                    <div class=\"\">\n", "                        刚刚\n", "                        看过\n", "                        \n", "                    </div>\n", "                </div>\n", "            </li>\n", "            \n", "            <li class=\"\">\n", "                <a href=\"https://www.douban.com/people/171767511/\" class=\"others-interest-avatar\">\n", "                    <img src=\"https://img2.doubanio.com/icon/u171767511-1.jpg\" class=\"pil\" alt=\"Gemini\">\n", "                </a>\n", "                <div class=\"others-interest-info\">\n", "                    <a href=\"https://www.douban.com/people/171767511/\" class=\"\">Gemini</a>\n", "                    <div class=\"\">\n", "                        刚刚\n", "                        看过\n", "                        <span class=\"allstar40\" title=\"推荐\"></span>\n", "                    </div>\n", "                </div>\n", "            </li>\n", "            \n", "            <li class=\"\">\n", "                <a href=\"https://www.douban.com/people/231995283/\" class=\"others-interest-avatar\">\n", "                    <img src=\"https://img2.doubanio.com/icon/u231995283-2.jpg\" class=\"pil\" alt=\"<PERSON>\">\n", "                </a>\n", "                <div class=\"others-interest-info\">\n", "                    <a href=\"https://www.douban.com/people/231995283/\" class=\"\"><PERSON></a>\n", "                    <div class=\"\">\n", "                        刚刚\n", "                        看过\n", "                        <span class=\"allstar30\" title=\"还行\"></span>\n", "                    </div>\n", "                </div>\n", "            </li>\n", "    </ul>\n", "\n", "    \n", "    <div class=\"subject-others-interests-ft\">\n", "        \n", "            <a href=\"https://movie.douban.com/subject/********/comments?status=P\">175925人看过</a>\n", "                &nbsp;/&nbsp;\n", "            <a href=\"https://movie.douban.com/subject/********/comments?status=F\">79878人想看</a>\n", "    </div>\n", "\n", "</div>\n", "\n", "\n", "\n", "    \n", "\n", "<!-- douban ad begin -->\n", "<div id=\"dale_movie_subject_middle_right\"></div>\n", "<script type=\"text/javascript\">\n", "    (function (global) {\n", "        if(!document.getElementsByClassName) {\n", "            document.getElementsByClassName = function(className) {\n", "                return this.querySelectorAll(\".\" + className);\n", "            };\n", "            Element.prototype.getElementsByClassName = document.getElementsByClassName;\n", "\n", "        }\n", "        var articles = global.document.getElementsByClassName('article'),\n", "            asides = global.document.getElementsByClassName('aside');\n", "\n", "        if (articles.length > 0 && asides.length > 0 && articles[0].offsetHeight >= asides[0].offsetHeight) {\n", "            (global.DoubanAdSlots = global.DoubanAdSlots || []).push('dale_movie_subject_middle_right');\n", "        }\n", "    })(this);\n", "</script>\n", "<!-- douban ad end -->\n", "\n", "\n", "\n", "    <br/>\n", "\n", "    \n", "<p class=\"pl\">订阅坚如磐石的评论: <br/><span class=\"feed\">\n", "    <a href=\"https://movie.douban.com/feed/subject/********/reviews\"> feed: rss 2.0</a></span></p>\n", "\n", "\n", "            </div>\n", "            <div class=\"extra\">\n", "                \n", "    \n", "<!-- douban ad begin -->\n", "<div id=\"dale_movie_subject_bottom_super_banner\"></div>\n", "<script type=\"text/javascript\">\n", "    (function (global) {\n", "        var body = global.document.body,\n", "            html = global.document.documentElement;\n", "\n", "        var height = Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight);\n", "        if (height >= 2000) {\n", "            (global.DoubanAdSlots = global.DoubanAdSlots || []).push('dale_movie_subject_bottom_super_banner');\n", "        }\n", "    })(this);\n", "</script>\n", "<!-- douban ad end -->\n", "\n", "    <!-- douban ad begin -->\n", "    <div id=\"dale_movie_subject_hovering_video\"></div>\n", "    <!-- douban ad end -->\n", "\n", "            </div>\n", "        </div>\n", "    </div>\n", "\n", "        \n", "    <div id=\"footer\">\n", "            <div class=\"footer-extra\"></div>\n", "        \n", "<span id=\"icp\" class=\"fleft gray-link\">\n", "    &copy; 2005－2023 douban.com, all rights reserved 北京豆网科技有限公司\n", "</span>\n", "\n", "<a href=\"https://www.douban.com/hnypt/variformcyst.py\" style=\"display: none;\"></a>\n", "\n", "<span class=\"fright\">\n", "    <a href=\"https://www.douban.com/about\">关于豆瓣</a>\n", "    · <a href=\"https://www.douban.com/jobs\">在豆瓣工作</a>\n", "    · <a href=\"https://www.douban.com/about?topic=contactus\">联系我们</a>\n", "    · <a href=\"https://www.douban.com/about/legal\">法律声明</a>\n", "    \n", "    · <a href=\"https://help.douban.com/?app=movie\" target=\"_blank\">帮助中心</a>\n", "    · <a href=\"https://www.douban.com/doubanapp/\">移动应用</a>\n", "    · <a href=\"https://www.douban.com/partner/\">豆瓣广告</a>\n", "</span>\n", "\n", "    </div>\n", "\n", "    </div>\n", "    <script type=\"text/javascript\">\n", "$(function(){\n", "    var $actor_list = $('#info .actor a');\n", "    var $actor_attr = $('#info .actor .attrs');\n", "\n", "    if ($actor_list.length > 5) {\n", "        $actor_attr.empty();\n", "\n", "        $actor_list.each(function(idx){\n", "            if (idx+1 === $actor_list.length) {\n", "                $('<span></span>').prepend($(this)).appendTo($actor_attr);\n", "            } else {\n", "                $('<span> / </span>').prepend($(this)).appendTo($actor_attr);\n", "            }\n", "        });\n", "\n", "        $('<a href=\"javascript:;\" class=\"more-actor\" title=\"更多主演\">更多...</a>').on('click', function(){\n", "            $actor_attr.find('span').show();\n", "            $(this).hide();\n", "        }).appendTo($actor_attr);\n", "\n", "        $actor_attr.find('span').eq(4).nextAll('span').hide();\n", "    }\n", "})\n", "</script>\n", "        \n", "        \n", "    <link rel=\"stylesheet\" type=\"text/css\" href=\"https://img1.doubanio.com/f/vendors/0035bb2f83e2cba49ecf634fed57f9ff1bbd0d09/css/ui/dialog.css\" />\n", "    <link rel=\"stylesheet\" type=\"text/css\" href=\"https://img1.doubanio.com/f/movie/9dbc931479d6c183e970ea3594672460b6c96b3c/dist/movie/mod/login_pop.css\" />\n", "    <script type=\"text/javascript\" src=\"https://img1.doubanio.com/f/vendors/f25ae221544f39046484a823776f3aa01769ee10/js/ui/dialog.js\"></script>\n", "    <script type=\"text/javascript\">\n", "        var HTTPS_DB = \"https://www.douban.com\"\n", "    </script>\n", "    <script type=\"text/javascript\" src=\"https://img1.doubanio.com/f/movie/3897d4734502adbdaa37cdf7f5c67a8e543ceefe/dist/movie/mod/login_pop.js\"></script>\n", "\n", "    \n", "    <script src=\"https://img1.doubanio.com/f/vendors/f25ae221544f39046484a823776f3aa01769ee10/js/ui/dialog.js\"></script>\n", "    <script src=\"https://img1.doubanio.com/f/movie/3d4f8e4a8918718256450eb6e57ec8e1f7a2e14b/dist/movie/libs/handlebars.js\"></script>\n", "    <script src=\"https://img1.doubanio.com/f/vendors/8d311a1defb6b721d73cfb891924c13c06af631b/js/ui/fix_aside.js\"></script>\n", "    <script src=\"https://img1.doubanio.com/f/vendors/55caa4decc5b4d78a5afa5d12f0b4bdfa7dcb6bb/js/lib/jquery.ba-hashchange.min.js\"></script>\n", "    <script src=\"https://img1.doubanio.com/f/movie/a58210ac28f14c3878942b24ec2212af19e6a0ab/dist/movie/subject/comment_vote.js\"></script>\n", "    \n", "    <script>\n", "        $(function(){\n", "            $().fixSide(300, 52);\n", "\n", "            var UA = navigator.userAgent.toLowerCase();\n", "            isSafari = /safari/.test(UA)\n", "            $(\"#season\").on('change', function(){\n", "                var subjectId = $(this).val();\n", "                if (subjectId.length){\n", "                    window.location = \"/subject/\" + subjectId + \"/\";\n", "                    // fix null state bug in Safari\n", "                    if (is<PERSON><PERSON><PERSON>) {\n", "                        window.history.pushState &&\n", "                          window.history.pushState(null, document.title, window.location.href)\n", "                    }\n", "                }\n", "            });\n", "            $(\"#season\").val(\"********\")\n", "\n", "            $(window).hashchange();\n", "            if(window.location.href.indexOf('suggest')>0 && window.location.href.indexOf(\"?\")>0){\n", "                if(window.history.pushState){\n", "                    window.history.pushState(null, document.title, window.location.href.substring(0,window.location.href.indexOf(\"?\")));\n", "                }\n", "            }\n", "\n", "            $('.indent .a_show_full').click(function(){\n", "                $.get('/blank?expand');\n", "            });\n", "\n", "            $('body').on('click', 'a.write_review', function(e) {\n", "                e.prevent<PERSON><PERSON><PERSON>();\n", "                if(window._USER_ABNORMAL) {\n", "                    show_abnormal && show_abnormal()\n", "                }else {\n", "                    location.href = $(this).attr('href')\n", "                }\n", "            })\n", "        });\n", "    </script>\n", "    \n", "    \n", "\n", "\n", "\n", "\n", "    \n", "<script type=\"text/javascript\">\n", "    (function (global) {\n", "        var newNode = global.document.createElement('script'),\n", "            existingNode = global.document.getElementsByTagName('script')[0],\n", "            adSource = '//erebor.douban.com/',\n", "            userId = '',\n", "            browserId = '0YlicXaSSiU',\n", "            criteria = '7:徐子力|7:王迅|7:犯罪|7:赵亮|7:动作|7:中国大陆|7:剧情|7:李乃文|7:周冬雨|7:孙艺洲|7:张国立|7:陈童|7:许亚军|7:田雨|7:警匪|7:陈道明|7:雷佳音|7:张艺谋|7:何政军|7:陈冲|7:悬疑|7:林博洋|7:焦刚|7:于和伟|7:2023|3:/subject/********/?from=showing',\n", "            preview = '',\n", "            debug = false,\n", "            adSlots = ['dale_movie_subject_top_right', 'dale_movie_subject_inner_middle', 'dale_movie_subject_banner_after_intro', 'dale_subject_right_guess_you_like', 'dale_movie_subject_hovering_video'];\n", "\n", "        global.DoubanAdRequest = {src: adSource, uid: userId, bid: browserId, crtr: criteria, prv: preview, debug: debug};\n", "        global.DoubanAdSlots = (global.DoubanAdSlots || []).concat(adSlots);\n", "\n", "        newNode.setAttribute('type', 'text/javascript');\n", "        newNode.setAttribute('src', '//img1.doubanio.com/aHF4dWV0OC9mL2FkanMvYzQyYzU5YWEwNWM4YTkyOTE3Y2UzM2RkZGEwZDk0OWYwZGM0YjVjNC9hZC5yZWxlYXNlLmpz?company_token=kX69T8w1wyOE-dale');\n", "        newNode.setAttribute('async', true);\n", "        existingNode.parentNode.insertBefore(newNode, existingNode);\n", "    })(this);\n", "</script>\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "    \n", "  \n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "<script type=\"text/javascript\">\n", "var _paq = _paq || [];\n", "_paq.push(['trackPageView']);\n", "_paq.push(['enableLinkTracking']);\n", "(function() {\n", "    var p=(('https:' == document.location.protocol) ? 'https' : 'http'), u=p+'://fundin.douban.com/';\n", "    _paq.push(['setTrackerUrl', u+'piwik']);\n", "    _paq.push(['setSiteId', '100001']);\n", "    var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];\n", "    g.type='text/javascript';\n", "    g.defer=true;\n", "    g.async=true;\n", "    g.src=p+'://img3.doubanio.com/dae/fundin/piwik.js';\n", "    s.parentNode.insertBefore(g,s);\n", "})();\n", "</script>\n", "\n", "<script type=\"text/javascript\">\n", "var setMethodWithNs = function(namespace) {\n", "  var ns = namespace ? namespace + '.' : ''\n", "    , fn = function(string) {\n", "        if(!ns) {return string}\n", "        return ns + string\n", "      }\n", "  return fn\n", "}\n", "\n", "var gaWithNamespace = function(fn, namespace) {\n", "  var method = setMethodWithNs(namespace)\n", "  fn.call(this, method)\n", "}\n", "\n", "var _gaq = _gaq || []\n", "  , accounts = [\n", "      { id: 'UA-7019765-1', namespace: 'douban' }\n", "    , { id: 'UA-7019765-19', namespace: '' }\n", "    ]\n", "  , gaInit = function(account) {\n", "      gaWithNamespace(function(method) {\n", "        gaInitFn.call(this, method, account)\n", "      }, account.namespace)\n", "    }\n", "  , gaInitFn = function(method, account) {\n", "      _gaq.push([method('_setAccount'), account.id]);\n", "      _gaq.push([method('_setSampleRate'), '5']);\n", "\n", "      \n", "  _gaq.push([method('_addOrganic'), 'google', 'q'])\n", "  _gaq.push([method('_addOrganic'), 'baidu', 'wd'])\n", "  _gaq.push([method('_addOrganic'), 'soso', 'w'])\n", "  _gaq.push([method('_addOrganic'), 'youdao', 'q'])\n", "  _gaq.push([method('_addOrganic'), 'so.360.cn', 'q'])\n", "  _gaq.push([method('_addOrganic'), 'sogou', 'query'])\n", "  if (account.namespace) {\n", "    _gaq.push([method('_addIgnoredOrganic'), '豆瓣'])\n", "    _gaq.push([method('_addIgnoredOrganic'), 'douban'])\n", "    _gaq.push([method('_addIgnoredOrganic'), '豆瓣网'])\n", "    _gaq.push([method('_addIgnoredOrganic'), 'www.douban.com'])\n", "  }\n", "\n", "      if (account.namespace === 'douban') {\n", "        _gaq.push([method('_setDomainName'), '.douban.com'])\n", "      }\n", "\n", "        _gaq.push([method('_setCustomVar'), 1, 'responsive_view_mode', 'desktop', 3])\n", "\n", "        _gaq.push([method('_setCustomVar'), 2, 'login_status', '0', 2]);\n", "\n", "      _gaq.push([method('_trackPageview')])\n", "    }\n", "\n", "for(var i = 0, l = accounts.length; i < l; i++) {\n", "  var account = accounts[i]\n", "  gaInit(account)\n", "}\n", "\n", "  gaWithNamespace(function(method) {\n", "    _gaq.push([method('_setAccount'), 'UA-59230-10'])\n", "    \n", "  _gaq.push([method('_addOrganic'), 'google', 'q'])\n", "  _gaq.push([method('_addOrganic'), 'baidu', 'wd'])\n", "  _gaq.push([method('_addOrganic'), 'soso', 'w'])\n", "  _gaq.push([method('_addOrganic'), 'youdao', 'q'])\n", "  _gaq.push([method('_addOrganic'), 'so.360.cn', 'q'])\n", "  _gaq.push([method('_addOrganic'), 'sogou', 'query'])\n", "  if (account.namespace) {\n", "    _gaq.push([method('_addIgnoredOrganic'), '豆瓣'])\n", "    _gaq.push([method('_addIgnoredOrganic'), 'douban'])\n", "    _gaq.push([method('_addIgnoredOrganic'), '豆瓣网'])\n", "    _gaq.push([method('_addIgnoredOrganic'), 'www.douban.com'])\n", "  }\n", "\n", "    _gaq.push([method('_setDomainName'), '.douban.com'])\n", "    _gaq.push([method('_trackPageview')]);\n", "  }, 't1')\n", "\n", ";(function() {\n", "    var ga = document.createElement('script');\n", "    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';\n", "    ga.setAttribute('async', 'true');\n", "    document.documentElement.firstChild.appendChild(ga);\n", "})()\n", "</script>\n", "\n", "<script src=\"/js/boomerang.js?09\" type=\"text/javascript\"></script>\n", "<script type=\"text/javascript\">\n", "BOOMR.init({\n", "    user_ip: \"**************\",\n", "    beacon_url: \"/beacon.gif\",\n", "    site_domain: \".douban.com\",\n", "    BW: {\n", "        enabled: false\n", "    }\n", "});\n", "BOOMR.subscribe('before_beacon', function(o) {\n", "    if(o.t_done && o.t_done > 0 && o.t_done < 30) {\n", "        _gaq.push(['t1._trackEvent', 'Performance', 'done', '/subject/-/', o.t_done]);\n", "    }\n", "});\n", "var _now = new Date();\n", "if (typeof(_head_start)==typeof(_now)) {\n", "    var t_head = _now-_head_start;\n", "    var t_body = _now-_body_start;\n", "    if (t_head < 1000*60 && t_head > 0){\n", "        var _slow = t_head > 10*1000 ? 'slow ' : '';\n", "        _gaq.push(['t1._trackEvent', 'Performance', _slow + 'head', '/subject/-/', _now-_head_start]);\n", "        _gaq.push(['t1._trackEvent', 'Performance', _slow + 'body', '/subject/-/', _now-_body_start]);\n", "        BOOMR.plugins.RT.setTimer('t_head', t_head).setTimer('t_body', t_body);\n", "    }\n", "}\n", "</script>\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "      \n", "\n", "    <!-- dae-web-movie--default-7866df68d4-6n8nf-->\n", "\n", "  <script>_SPLITTEST=''</script>\n", "</body>\n", "\n", "</html>\n", "\n", "\n", "\n"]}], "source": ["url = 'https://movie.douban.com/subject/********/?from=showing'\n", "headers = {\n", "    'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.289 Safari/537.36'\n", "}\n", "response = requests.get(url,headers=headers)\n", "print(response.status_code)\n", "print(response.encoding)\n", "print(response.apparent_encoding)\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'5星': '7.8%', '4星': '26.9%', '3星': '44.8%', '2星': '15.6%', '1星': '5.0%'}"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["selector = parsel.Selector(response.text)\n", "avg_score = selector.css('strong.ll.rating_num::text').extract()\n", "ratings = selector.css('span.rating_per::text').extract()\n", "rating_stars = selector.css('span.starstop::text').extract()\n", "rating_stars = [item.strip() for item in rating_stars]\n", "rating_dict = dict(zip(rating_stars,ratings))\n", "rating_dict"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["['<span property=\"v:summary\" class=\"\">\\n                                \\u3000\\u3000金江市副市长郑刚（张国立 饰）之子苏见明（雷佳音 饰）不顾父亲的劝阻，应邀赴约首富黎志田（于和伟 饰）的“鸿门宴”，不料却被迫观看了一出“人手下火锅”的猖狂戏码。旧案翻起，风雨欲来，各方蛰伏势力蠢蠢欲动，筹谋与算计、审视与怀疑，光怪陆离之中，人性欲望翻腾，谁将撕去最后的面具？\\n                        </span>']"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["brief_intro = selector.css('span[property=\"v:summary\"]').extract()\n", "brief_intro"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}