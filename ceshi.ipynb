{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["```python\n", "import matplotlib.pyplot as plt\n", "plt.subplots(2,3)\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 这是我的测试案例代码"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 这也是我的测试案例代码"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 这是我的测试案例代码"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["array([  1.,   2.,   3.,   4.,   5.,   6.,   7.,   8.,   9.,  10.,  11.,\n", "        12.,  13.,  14.,  15.,  16.,  17.,  18.,  19.,  20.,  21.,  22.,\n", "        23.,  24.,  25.,  26.,  27.,  28.,  29.,  30.,  31.,  32.,  33.,\n", "        34.,  35.,  36.,  37.,  38.,  39.,  40.,  41.,  42.,  43.,  44.,\n", "        45.,  46.,  47.,  48.,  49.,  50.,  51.,  52.,  53.,  54.,  55.,\n", "        56.,  57.,  58.,  59.,  60.,  61.,  62.,  63.,  64.,  65.,  66.,\n", "        67.,  68.,  69.,  70.,  71.,  72.,  73.,  74.,  75.,  76.,  77.,\n", "        78.,  79.,  80.,  81.,  82.,  83.,  84.,  85.,  86.,  87.,  88.,\n", "        89.,  90.,  91.,  92.,  93.,  94.,  95.,  96.,  97.,  98.,  99.,\n", "       100.])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linspace(1,100,100)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["defaultdict(int, {('a', 'b'): 5, ('b', 'c'): 6, ('c', 'd'): 4, ('d', 'e'): 3})"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["from collections import defaultdict\n", "import re\n", "def get_stats(vocab):\n", "    \"\"\"\n", "    Given a vocabulary (dictionary mapping words to frequency counts), returns a \n", "    dictionary of tuples representing the frequency count of pairs of characters \n", "    in the vocabulary.\n", "    \"\"\"\n", "    pairs = defaultdict(int)\n", "    for word, freq in vocab.items():\n", "        symbols = word.split()\n", "        for i in range(len(symbols)-1):\n", "            pairs[symbols[i],symbols[i+1]] += freq\n", "    return pairs\n", "\n", "def merge_vocab(pair, v_in):\n", "    \"\"\"\n", "    Given a pair of characters and a vocabulary, returns a new vocabulary with the \n", "    pair of characters merged together wherever they appear.\n", "    \"\"\"\n", "    v_out = {}\n", "    bigram = re.escape(' '.join(pair))\n", "    p = re.compile(r'(?<!\\S)' + bigram + r'(?!\\S)')\n", "    # p = re.compile(bigram)\n", "    for word in v_in:\n", "        w_out = p.sub(''.join(pair), word)\n", "        v_out[w_out] = v_in[word]\n", "    return v_out\n", "\n", "vocab = {\"a b c d e\":3,\"a b c\":2,\"b c d\":1}\n", "pairs = get_stats(vocab)\n", "pairs"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'a b cd e': 3, 'a bc': 2, 'bc d': 1}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["vocab1 = {\"a b cd e\":3,\"a b c\":2,\"b c d\":1}\n", "best = max(pairs,key=pairs.get)\n", "merge_vocab(best,vocab1)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import jieba"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 这是我的另外一个测试代码"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["<generator object Tokenizer.cut at 0x00000224D1F928F0>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["seg_results = jieba.cut('我来自湖北武汉市洪山区保利蓝海郡')\n", "seg_results"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Dumping model to file cache C:\\Users\\<USER>\\AppData\\Local\\Temp\\jieba.cache\n", "Loading model cost 0.452 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["我\n", "来自\n", "湖北\n", "武汉市\n", "洪山区\n", "保利\n", "蓝海\n", "郡\n"]}], "source": ["for item in seg_results:\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'str' object has no attribute 'decode'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32me:\\test code\\ceshi.ipynb 单元格 11\u001b[0m line \u001b[0;36m1\n\u001b[1;32m----> <a href='vscode-notebook-cell:/e%3A/test%20code/ceshi.ipynb#X21sZmlsZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39mprint\u001b[39m(\u001b[39m'\u001b[39;49m\u001b[39m\\ue333\u001b[39;49;00m\u001b[39m'\u001b[39;49m\u001b[39m.\u001b[39;49mdecode(\u001b[39m'\u001b[39m\u001b[39mutf-8\u001b[39m\u001b[39m'\u001b[39m))\n", "\u001b[1;31mAttributeError\u001b[0m: 'str' object has no attribute 'decode'"]}], "source": ["print('s')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["<matplotlib.collections.PathCollection at 0x224d7aa8a00>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"name": "stderr", "output_type": "stream", "text": ["D:\\Program Files\\anaconda3\\lib\\site-packages\\IPython\\core\\events.py:89: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from current font.\n", "  func(*args, **kwargs)\n", "D:\\Program Files\\anaconda3\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 900x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "\n", "\n", "x_values = np.random.randint(1,100,20)\n", "y_values = np.random.randint(1,50,20)\n", "\n", "fig = plt.figure(figsize=(9,6))\n", "axes = fig.add_axes([0,0,1,1])\n", "axes.set_xlabel('x值')\n", "axes.set_ylabel('y值')\n", "axes.scatter(x_values,y_values)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n"]}, {"data": {"text/plain": ["<matplotlib.legend.Legend at 0x224d84cf9a0>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1100x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x = np.arange(5)\n", "province_list = ['guangzhou','wuhan','hangzhou','beijing','shanghai']\n", "gdp_arr = [[3000,2300,2600,5600,5200],[2900,2600,2700,4900,4800],[2400,2690,2300,5900,5320]]\n", "\n", "fig = plt.figure(figsize=(11,8))\n", "ax = fig.add_axes([0,0,1,1])\n", "bar1 = ax.bar(x, gdp_arr[0],width=0.2)\n", "bar2 = ax.bar(x+0.21, gdp_arr[1],width=0.2)\n", "bar3 = ax.bar(x+0.42,gdp_arr[2],width=0.2)\n", "ax.set_xticks(x+0.2,province_list)\n", "ax.bar_label(bar1,fmt='{:,.0f}',label_type='edge',padding=5,fontsize=15)\n", "ax.bar_label(bar2,label_type='edge',padding=5,fontsize=15)\n", "ax.bar_label(bar3,label_type='edge',padding=5,fontsize=15)\n", "ax.legend()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1100x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib\n", "\n", "x = np.arange(5)\n", "province_list = ['广州','武汉','杭州','北京','上海']\n", "gdp_arr = [[3000,2300,2600,5600,5200],[2900,2600,2700,4900,4800],[2400,2690,2300,5900,5320]]\n", "\n", "fig = plt.figure(figsize=(11,6))\n", "ax = fig.add_axes([0,0,1,1])\n", "multiplier = 0\n", "for i in range(3):\n", "    offset = multiplier*0.2\n", "    bar_temp = ax.bar(x+offset, gdp_arr[i],width=0.2,label=province_list[i])\n", "    ax.bar_label(bar_temp,padding=5)\n", "    multiplier+=1\n", "\n", "ax.set_yticks(np.arange(0,6001,1000),np.arange(0,6001,1000),fontsize=15)\n", "ax.set_xticks(x+0.2, province_list,fontsize=15,fontfamily='SimHei')\n", "ax.legend(loc='upper left',bbox_to_anchor=(1,1),fontsize=18)\n", "ax.set_facecolor('#F6EDD9')\n", "ax.grid(axis='y',dashes=(8,3,3,2))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["int"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["type(gdp_arr[0][0])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 2}