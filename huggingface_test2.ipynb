{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from transformers import AutoModelForSequenceClassification, AutoTokenizer\n", "import torch"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d4550f41725249ec9810fbc580c7fc76", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/295 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "71eb54b04f9e4247a37eb6b88e5ee695", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/612 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bac3c0192c4a4b9faa663407a88bc421", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.txt:   0%|          | 0.00/110k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8130549ee7974170a60686780fc8d530", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/112 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e3c8f462109e425493d68752ddd81440", "version_major": 2, "version_minor": 0}, "text/plain": ["pytorch_model.bin:   0%|          | 0.00/409M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tokenizer = AutoTokenizer.from_pretrained(\"uer/roberta-base-finetuned-dianping-chinese\")\n", "model = AutoModelForSequenceClassification.from_pretrained(\"uer/roberta-base-finetuned-dianping-chinese\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["BertTokenizerFast(name_or_path='uer/roberta-base-finetuned-dianping-chinese', vocab_size=21128, model_max_length=1000000000000000019884624838656, is_fast=True, padding_side='right', truncation_side='right', special_tokens={'unk_token': '[UNK]', 'sep_token': '[SEP]', 'pad_token': '[PAD]', 'cls_token': '[CLS]', 'mask_token': '[MASK]'}, clean_up_tokenization_spaces=True),  added_tokens_decoder={\n", "\t0: AddedToken(\"[PAD]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "\t100: AddedToken(\"[UNK]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "\t101: AddedToken(\"[CLS]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "\t102: AddedToken(\"[SEP]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "\t103: AddedToken(\"[MASK]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["BertConfig {\n", "  \"_name_or_path\": \"uer/roberta-base-finetuned-dianping-chinese\",\n", "  \"architectures\": [\n", "    \"BertForSequenceClassification\"\n", "  ],\n", "  \"attention_probs_dropout_prob\": 0.1,\n", "  \"classifier_dropout\": null,\n", "  \"hidden_act\": \"gelu\",\n", "  \"hidden_dropout_prob\": 0.1,\n", "  \"hidden_size\": 768,\n", "  \"id2label\": {\n", "    \"0\": \"negative (stars 1, 2 and 3)\",\n", "    \"1\": \"positive (stars 4 and 5)\"\n", "  },\n", "  \"initializer_range\": 0.02,\n", "  \"intermediate_size\": 3072,\n", "  \"label2id\": {\n", "    \"negative (stars 1, 2 and 3)\": 0,\n", "    \"positive (stars 4 and 5)\": 1\n", "  },\n", "  \"layer_norm_eps\": 1e-12,\n", "  \"max_position_embeddings\": 512,\n", "  \"model_type\": \"bert\",\n", "  \"num_attention_heads\": 12,\n", "  \"num_hidden_layers\": 12,\n", "  \"pad_token_id\": 0,\n", "  \"position_embedding_type\": \"absolute\",\n", "  \"transformers_version\": \"4.35.2\",\n", "  \"type_vocab_size\": 2,\n", "  \"use_cache\": true,\n", "  \"vocab_size\": 21128\n", "}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["model.config"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input_ids': tensor([[ 101, 5831, 1501, 3173, 7831, 8024, 2397, 1112, 8024, 1310, 4495, 8024,\n", "         5445,  684,  671, 1147, 6963, 3221, 6851, 3209, 4638, 8024, 7560, 2145,\n", "         6963, 5543, 4692, 1168, 5831, 1501, 1169,  868, 6814, 4923,  511, 6375,\n", "          782, 2523, 3123, 2552,  511, 1456, 6887,  738, 2523, 1962, 8024, 6793,\n", "         2533, 2523, 3633, 2134,  511, 4528, 5746, 3739, 1456, 6887, 7831, 5401,\n", "          511, 6820,  981, 6878, 3302, 1218, 1447, 6663, 5659, 8024,  679, 2533,\n", "          679, 2697, 1386, 8024, 4385, 1762, 3766, 4157, 2798, 5686, 6825, 3302,\n", "         1218, 1447, 6963, 2496,  679,  749,  749, 1557,  511,  102],\n", "        [ 101, 5050, 3221, 2110, 3413, 7353, 6818, 1456, 6887, 6820, 6121, 4638,\n", "         4125, 7222, 2421,  749,  117,  712, 6206, 4294, 1166, 4294, 1166,  912,\n", "         2139, 1391,  749, 1962, 1914, 3613, 3680, 3613, 3341, 6963, 6206, 2961,\n", "         7339,  511,  102,    0,    0,    0,    0,    0,    0,    0,    0,    0,\n", "            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,\n", "            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,\n", "            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,\n", "            0,    0,    0,    0,    0,    0,    0,    0,    0,    0],\n", "        [ 101, 4495, 2692, 4638, 4125, 4255, 4923, 2428, 1922, 6375,  782, 2692,\n", "         1912,  749,  511, 3241,  677, 1063, 4157,  679, 1168, 8024, 2147, 1079,\n", "         2147, 1912, 4638, 2429,  855, 6963, 1777, 4007,  749, 8024, 6820, 3300,\n", "         2523, 1914,  782, 5023,  855,  511, 6006, 4197,  791, 1921, 2170, 7599,\n", "         1173, 7755, 8024,  793, 4197, 2913,  679,  857, 1391, 2145, 4638, 4178,\n", "         2658,  511, 1730, 6579,  749,  676, 2476, 1282, 1039,  807,  758, 1282,\n", "         4638, 1171, 8024,  676,  782, 1920, 2571, 3321, 7573, 4638, 1391,  119,\n", "          119,  119,  119,  119,  119,  102,    0,    0,    0,    0]]), 'token_type_ids': tensor([[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "         0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "         0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "         0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n", "        [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "         0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "         0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "         0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n", "        [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "         0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "         0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "         0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]), 'attention_mask': tensor([[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "         1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "         1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "         1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],\n", "        [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "         1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "         0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "         0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n", "        [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "         1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "         1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "         1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0]])}"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["text = [\n", "        \"菜品新鲜，干净，卫生，而且一切都是透明的，顾客都能看到菜品制作过程。让人很放心。味道也很好，辣得很正宗。番茄汤味道鲜美。还偶遇服务员跳舞，不得不感叹，现在没点才艺连服务员都当不了了啊。\",\n", "        \"算是学校附近味道还行的火锅店了,主要特别特别便宜 吃了好多次 每次来都要排队。\",\n", "        \"生意的火爆程度太让人意外了。晚上六点不到，室内室外的座位都坐满了，还有很多人等位。虽然今天寒风刺骨，仍然挡不住吃客的热情。团购了三张十元代五十的券，三人大快朵颐的吃......\"\n", "        ]\n", "inputs = tokenizer(text,padding=True,return_tensors='pt')\n", "inputs"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["with torch.no_grad():\n", "    logits = model(**inputs).logits"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["评论------\"菜品新鲜，干净，卫生，而且一切都是透明的，顾客都能看到菜品制作过程。让人很放心。味道也很好，辣得很正宗。番茄汤味道鲜美。还偶遇服务员跳舞，不得不感叹，现在没点才艺连服务员都当不了了啊。\"的情感标签为：\n", "positive (stars 4 and 5)\n", "评论------\"算是学校附近味道还行的火锅店了,主要特别特别便宜 吃了好多次 每次来都要排队。\"的情感标签为：\n", "negative (stars 1, 2 and 3)\n", "评论------\"生意的火爆程度太让人意外了。晚上六点不到，室内室外的座位都坐满了，还有很多人等位。虽然今天寒风刺骨，仍然挡不住吃客的热情。团购了三张十元代五十的券，三人大快朵颐的吃......\"的情感标签为：\n", "positive (stars 4 and 5)\n"]}], "source": ["predicted_class_id = logits.argmax(dim=1).tolist()\n", "for comments,id in zip(text,predicted_class_id):\n", "    predicted_class_label = model.config.id2label[id]\n", "    print(f\"评论------\\\"{comments}\\\"的情感标签为：\\n{predicted_class_label}\")"]}], "metadata": {"kernelspec": {"display_name": "pytorchl", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}