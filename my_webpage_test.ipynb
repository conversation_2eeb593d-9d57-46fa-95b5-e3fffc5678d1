{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import parsel"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<!DOCTYPE html>\\n<html>\\n\\t<head>\\n\\t\\t<meta charset=\\'utf-8\\'>\\n\\t\\t<title>这是一个测试页面</title>\\n\\t</head>\\n\\t<body>\\n        <div class=\"xiaoming\">\\n            <h2>这是小明和小红的故事</h2>\\n            <p><b>故事概要</b></p>\\n            <ul class=\"ul\">\\n                <li class=\"first-sight\">相识</li>\\n                    <p>小明和小红在学校相识已有一段时间，他们的友情逐渐深化，但也渐渐有了更多的感情。一天，他们一起去了学校附近的公园散步，正是春天的季节，鲜花绽放，阳光明媚。他们沿着小径漫步，聊着天，享受着这个美好的时光</p>\\n                <li class=\"problem\">困局</li>\\n                    <p>在公园里，他们突然遇到了一个困难的局面。有只小狗迷路了，它看起来很害怕。小明和小红决定一起帮助这只小狗。他们花了一些时间，终于找到了小狗的主人，并把它送回了家。这个经历让他们更加亲近，因为他们共同克服了一个困难，体验到了合作的重要性</p>\\n                <li class=\"problem\">相知</li>\\n                    <p>随着时间的推移，小明和小红的感情逐渐升温。他们开始更频繁地一起做各种事情，比如一起看电影、一起做饭、一起去旅行。每一次的相处都让他们更加了解对方，也让他们感到彼此的陪伴是多么的愉快</p>\\n                <li class=\"problem\">相处</li>\\n                    <p>小明和小红都有一个共同的梦想，那就是一起建立自己的<a href=\"https://www.hhax.org/\" target=\"_blank\">慈善组织</a>，帮助那些需要帮助的人。他们开始积攒资金，制定计划，慢慢地把这个梦想变成了现实。他们的慈善组织得到了越来越多人的支持，一些志愿者也加入进来，共同为社会做出了贡献</p>\\n                <li class=\"problem\">相爱</li>\\n                    <p>最终，小明向小红表白了，告诉她自己有多么喜欢她。小红感到非常的感动，因为她早已对小明有着同样的感情。他们的友情和爱情在这个特殊的时刻融为一体，成为了更加坚固的纽带。从此以后，他们一起走过了生活中的每一个阶段，共同建立了幸福的未来</p>\\n            </ul>\\n        </div>\\n        <hr/>\\n        <hr/>\\n        <div class=\"xiaoqiang\">\\n            <p> <br> </p>\\n            <p> <br> </p>\\n            <h2>这是小强和小林的故事</h2>\\n            <p><b>故事概要</b></p>\\n            <ul class=\"ul\">\\n                <li class=\"first-sight\">遇见</li>\\n                    <p>在一个阳光明媚的春日，小强和小林在大学校园里偶然相遇。小强是一名文学系的学生，沉迷于诗歌和小说；而小林则是工程系的学霸，专注于数字世界的奥秘。他们在图书馆前的樱花树下第一次相遇，那一刻，两颗年轻的心开始跳动。</p>\\n                <li class=\"problem\">互相吸引</li>\\n                    <p>小强和小林虽然兴趣和专业不同，但却被彼此的聪明和独立吸引。他们开始一起参加学术讨论会和课外活动，慢慢地建立了深厚的友情。小强被小林的才华和幽默所吸引，而小林则被小强的独特见解和灵感所倾倒。</p>\\n                <li class=\"problem love\">爱情初萌</li>\\n                    <p>随着时间的推移，小强和小林的友情逐渐升华为爱情。他们开始分享更多的时间，共同探索城市，品味美食，享受音乐会。每一刻都让他们感到生活是如此美好，而彼此则是最美丽的风景。</p>\\n                <li class=\"secret\">隐瞒的秘密</li>\\n                    <p>然而，小林一直有一个隐瞒的秘密。他曾经是一名黑客，曾参与了一些非法活动。虽然他已经远离了这个世界，但是他一直担心过去的罪行会影响到他们的未来。他一直没有告诉小强，因为他不想失去她。</p>\\n                <li class=\"secret\">揭示真相</li>\\n                    <p>有一天，小林的过去再次找上门。一名神秘的黑客开始威胁他，要揭露他的黑暗历史。小林陷入了困境，不知所措。在深夜，他决定告诉小强一切，包括他的过去和当前的困境。</p>\\n                <li class=\"problem\">爱的力量</li>\\n                    <p>小强听完小林的坦白后，没有生气，也没有责怪。相反，她鼓励他面对过去，与黑客对抗。他们一起展开了一场冒险，用爱的力量克服了困难。最终，他们战胜了黑客，而小林也摆脱了过去的阴影。他们的爱情更加坚不可摧，这段经历也让他们更加深刻地理解了彼此。小强和小林的爱情故事充满了坎坷，但也充满了希望和信仰，他们决定一起走向未来，共同创造美好的故事。</p>\\n            </ul>\\n        </div>\\n        <hr/>\\n        <form>\\n            <div style=\"font-size: 30px; font-family: 楷体\">你更喜欢哪个故事:</div><br/>\\n            <input type=\"radio\" name=\"gushi\"><font size=\"3\">\"小明和小红\"</font>\\n            <input type=\"radio\" name=\"gushi\"><font size=\"3\">\"小强和小红\"</font>\\n        </form>\\n        <hr/>\\n        \\n\\t</body>\\n</html>'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["f = open('my_webpage.html',mode='r',encoding='utf-8')\n", "html = f.read()\n", "html"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["这是小强和小林的故事\n"]}], "source": ["selector = parsel.Selector(html)\n", "text = selector.css('div.xiaoqiang h2::text').get()\n", "print(text)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['这是小明和小红的故事', '这是小强和小林的故事']\n"]}], "source": ["text = selector.css('h2::text').extract()\n", "print(text)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['然而，小林一直有一个隐瞒的秘密。他曾经是一名黑客，曾参与了一些非法活动。虽然他已经远离了这个世界，但是他一直担心过去的罪行会影响到他们的未来。他一直没有告诉小强，因为他不想失去她。', '有一天，小林的过去再次找上门。一名神秘的黑客开始威胁他，要揭露他的黑暗历史。小林陷入了困境，不知所措。在深夜，他决定告诉小强一切，包括他的过去和当前的困境。', '小强听完小林的坦白后，没有生气，也没有责怪。相反，她鼓励他面对过去，与黑客对抗。他们一起展开了一场冒险，用爱的力量克服了困难。最终，他们战胜了黑客，而小林也摆脱了过去的阴影。他们的爱情更加坚不可摧，这段经历也让他们更加深刻地理解了彼此。小强和小林的爱情故事充满了坎坷，但也充满了希望和信仰，他们决定一起走向未来，共同创造美好的故事。']\n"]}], "source": ["text = selector.css('.secret~p::text').extract()\n", "print(text)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['互相吸引']\n"]}], "source": ["text = selector.css('.x<PERSON><PERSON><PERSON><PERSON> ul li:nth-child(3)::text').getall()\n", "print(text)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["'radio'"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["selector.css(\"input\").attrib[\"type\"]"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}